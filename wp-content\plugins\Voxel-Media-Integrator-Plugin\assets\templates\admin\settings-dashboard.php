<?php
/**
 * Template for displaying settings dashboard
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-settings">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <?php if ($settings_warnings > 0): ?>
        <div class="vmi-warning-message">
            <?php echo esc_html(sprintf(
                _n(
                    'You have %d setting that needs attention.',
                    'You have %d settings that need attention.',
                    $settings_warnings,
                    'voxel-media-integrator'
                ),
                $settings_warnings
            )); ?>
        </div>
    <?php endif; ?>

    <div class="vmi-stats-grid">
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-media-default"></span>
                <?php esc_html_e('Total Media Items', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_tours + $published_models + $published_videos + $published_courses); ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('Across all types', 'voxel-media-integrator'); ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-admin-plugins"></span>
                <?php esc_html_e('Active Media Types', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php
                $active_types = 0;
                if ($published_tours > 0) $active_types++;
                if ($published_models > 0) $active_types++;
                if ($published_videos > 0) $active_types++;
                if ($published_courses > 0) $active_types++;
                echo esc_html($active_types);
            ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('Media types in use', 'voxel-media-integrator'); ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-chart-area"></span>
                <?php esc_html_e('Storage Used', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php
                echo esc_html(size_format($total_storage));
            ?></p>
            <p class="vmi-stat-label"><?php
                echo esc_html(sprintf(
                    __('%s available', 'voxel-media-integrator'),
                    size_format($storage_limit - $total_storage)
                ));
            ?></p>
        </div>
    </div>

    <div class="vmi-quick-actions">
        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-settings&tab=vmi_general_settings')); ?>" class="button button-primary">
            <span class="dashicons dashicons-admin-generic"></span>
            <?php esc_html_e('General Settings', 'voxel-media-integrator'); ?>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-settings&tab=vmi_video_settings')); ?>" class="button">
            <span class="dashicons dashicons-video-alt3"></span>
            <?php esc_html_e('Video Settings', 'voxel-media-integrator'); ?>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-settings&tab=vmi_model_settings')); ?>" class="button">
            <span class="dashicons dashicons-media-interactive"></span>
            <?php esc_html_e('Model Settings', 'voxel-media-integrator'); ?>
        </a>
    </div>

    <div class="vmi-content-grid">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-admin-tools"></span>
                <?php esc_html_e('System Configuration', 'voxel-media-integrator'); ?>
            </h2>

            <div class="vmi-activity-item">
                <div class="vmi-activity-icon">
                    <span class="dashicons dashicons-<?php echo $api_status ? 'yes-alt' : 'warning'; ?>"></span>
                </div>
                <div class="vmi-activity-content">
                    <div class="vmi-activity-title">
                        <?php esc_html_e('API Configuration', 'voxel-media-integrator'); ?>
                    </div>
                    <div class="vmi-activity-meta">
                        <?php echo $api_status ?
                            esc_html__('All APIs configured', 'voxel-media-integrator') :
                            esc_html__('Some APIs need setup', 'voxel-media-integrator');
                        ?>
                    </div>
                </div>
            </div>

            <div class="vmi-activity-item">
                <div class="vmi-activity-icon">
                    <span class="dashicons dashicons-<?php echo $storage_status ? 'yes-alt' : 'warning'; ?>"></span>
                </div>
                <div class="vmi-activity-content">
                    <div class="vmi-activity-title">
                        <?php esc_html_e('Storage Configuration', 'voxel-media-integrator'); ?>
                    </div>
                    <div class="vmi-activity-meta">
                        <?php echo $storage_status ?
                            esc_html__('Storage optimized', 'voxel-media-integrator') :
                            esc_html__('Storage needs optimization', 'voxel-media-integrator');
                        ?>
                    </div>
                </div>
            </div>

            <div class="vmi-activity-item">
                <div class="vmi-activity-icon">
                    <span class="dashicons dashicons-<?php echo $security_status ? 'yes-alt' : 'warning'; ?>"></span>
                </div>
                <div class="vmi-activity-content">
                    <div class="vmi-activity-title">
                        <?php esc_html_e('Security Settings', 'voxel-media-integrator'); ?>
                    </div>
                    <div class="vmi-activity-meta">
                        <?php echo $security_status ?
                            esc_html__('Security configured', 'voxel-media-integrator') :
                            esc_html__('Security needs attention', 'voxel-media-integrator');
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-chart-pie"></span>
                <?php esc_html_e('Media Type Breakdown', 'voxel-media-integrator'); ?>
            </h2>

            <div class="vmi-type-distribution">
                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-visibility"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Virtual Tours', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d tour', '%d tours', $published_tours, 'voxel-media-integrator'),
                                $published_tours
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-media-interactive"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('3D Models', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d model', '%d models', $published_models, 'voxel-media-integrator'),
                                $published_models
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-video-alt3"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Videos', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d video', '%d videos', $published_videos, 'voxel-media-integrator'),
                                $published_videos
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-welcome-learn-more"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('LMS Courses', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d course', '%d courses', $published_courses, 'voxel-media-integrator'),
                                $published_courses
                            )); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>