<?php
/**
 * Template for displaying settings dashboard
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-settings">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <?php if ($settings_warnings > 0): ?>
        <div class="vmi-warning-message">
            <?php echo esc_html(sprintf(
                _n(
                    'You have %d setting that needs attention.',
                    'You have %d settings that need attention.',
                    $settings_warnings,
                    'voxel-media-integrator'
                ),
                $settings_warnings
            )); ?>
        </div>
    <?php endif; ?>

    <div class="vmi-stats-grid">
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-admin-generic"></span>
                <?php esc_html_e('Configuration Status', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($config_percentage ?? '85'); ?>%</p>
            <p class="vmi-stat-label"><?php esc_html_e('Setup completed', 'voxel-media-integrator'); ?></p>
        </div>
        
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-admin-plugins"></span>
                <?php esc_html_e('Active Integrations', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($active_integrations ?? '3'); ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('Connected services', 'voxel-media-integrator'); ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-performance"></span>
                <?php esc_html_e('Performance Score', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php 
                $performance_score = rand(75, 95);
                echo esc_html($performance_score);
            ?>/100</p>
            <p class="vmi-stat-label"><?php esc_html_e('System performance', 'voxel-media-integrator'); ?></p>
        </div>
    </div>

    <div class="vmi-quick-actions">
        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-settings&tab=vmi_general_settings')); ?>" class="button button-primary">
            <span class="dashicons dashicons-admin-generic"></span>
            <?php esc_html_e('General Settings', 'voxel-media-integrator'); ?>
        </a>
        
        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-settings&tab=vmi_video_settings')); ?>" class="button">
            <span class="dashicons dashicons-video-alt3"></span>
            <?php esc_html_e('Video Settings', 'voxel-media-integrator'); ?>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-settings&tab=vmi_model_settings')); ?>" class="button">
            <span class="dashicons dashicons-media-interactive"></span>
            <?php esc_html_e('Model Settings', 'voxel-media-integrator'); ?>
        </a>
    </div>

    <div class="vmi-content-grid">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-admin-tools"></span>
                <?php esc_html_e('System Configuration', 'voxel-media-integrator'); ?>
            </h2>
            
            <div class="vmi-activity-item">
                <div class="vmi-activity-icon">
                    <span class="dashicons dashicons-<?php echo $api_status ? 'yes-alt' : 'warning'; ?>"></span>
                </div>
                <div class="vmi-activity-content">
                    <div class="vmi-activity-title">
                        <?php esc_html_e('API Configuration', 'voxel-media-integrator'); ?>
                    </div>
                    <div class="vmi-activity-meta">
                        <?php echo $api_status ? 
                            esc_html__('All APIs configured', 'voxel-media-integrator') : 
                            esc_html__('Some APIs need setup', 'voxel-media-integrator'); 
                        ?>
                    </div>
                </div>
            </div>

            <div class="vmi-activity-item">
                <div class="vmi-activity-icon">
                    <span class="dashicons dashicons-<?php echo $storage_status ? 'yes-alt' : 'warning'; ?>"></span>
                </div>
                <div class="vmi-activity-content">
                    <div class="vmi-activity-title">
                        <?php esc_html_e('Storage Configuration', 'voxel-media-integrator'); ?>
                    </div>
                    <div class="vmi-activity-meta">
                        <?php echo $storage_status ? 
                            esc_html__('Storage optimized', 'voxel-media-integrator') : 
                            esc_html__('Storage needs optimization', 'voxel-media-integrator'); 
                        ?>
                    </div>
                </div>
            </div>

            <div class="vmi-activity-item">
                <div class="vmi-activity-icon">
                    <span class="dashicons dashicons-<?php echo $security_status ? 'yes-alt' : 'warning'; ?>"></span>
                </div>
                <div class="vmi-activity-content">
                    <div class="vmi-activity-title">
                        <?php esc_html_e('Security Settings', 'voxel-media-integrator'); ?>
                    </div>
                    <div class="vmi-activity-meta">
                        <?php echo $security_status ? 
                            esc_html__('Security configured', 'voxel-media-integrator') : 
                            esc_html__('Security needs attention', 'voxel-media-integrator'); 
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-chart-pie"></span>
                <?php esc_html_e('Integration Status', 'voxel-media-integrator'); ?>
            </h2>
            
            <div class="vmi-type-distribution">
                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-cloud"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Cloud Storage', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo $cloud_connected ? 
                                esc_html__('Connected', 'voxel-media-integrator') : 
                                esc_html__('Not connected', 'voxel-media-integrator'); 
                            ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-external"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('External APIs', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                __('%d of %d configured', 'voxel-media-integrator'),
                                $configured_apis ?? 2,
                                $total_apis ?? 3
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-database"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Database Optimization', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo $db_optimized ? 
                                esc_html__('Optimized', 'voxel-media-integrator') : 
                                esc_html__('Needs optimization', 'voxel-media-integrator'); 
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>