/**
 * Voxel Media Integrator - Modern Admin Styles
 */

/* Theme Color Variables */
.vmi-theme-models {
    --primary-color: #8b5cf6;
    --primary-light: #a78bfa;
    --primary-dark: #7c3aed;
    --bg-color: #faf5ff;
    --bg-gradient: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
    --card-gradient: linear-gradient(145deg, #ffffff 0%, #fefbff 100%);
}

.vmi-theme-videos {
    --primary-color: #ef4444;
    --primary-light: #f87171;
    --primary-dark: #dc2626;
    --bg-color: #fef2f2;
    --bg-gradient: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    --card-gradient: linear-gradient(145deg, #ffffff 0%, #fffbfb 100%);
}

.vmi-theme-tours {
    --primary-color: #10b981;
    --primary-light: #34d399;
    --primary-dark: #059669;
    --bg-color: #f0fdf4;
    --bg-gradient: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    --card-gradient: linear-gradient(145deg, #ffffff 0%, #fefffe 100%);
}

.vmi-theme-main {
    --primary-color: #3b82f6;
    --primary-light: #60a5fa;
    --primary-dark: #2563eb;
    --bg-color: #eff6ff;
    --bg-gradient: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    --card-gradient: linear-gradient(145deg, #ffffff 0%, #fbfcff 100%);
}

.vmi-theme-lms {
    --primary-color: #f59e0b;
    --primary-light: #fbbf24;
    --primary-dark: #d97706;
    --bg-color: #fffbeb;
    --bg-gradient: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    --card-gradient: linear-gradient(145deg, #ffffff 0%, #fffffe 100%);
}

.vmi-theme-settings {
    --primary-color: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --bg-color: #f1f5f9;
    --bg-gradient: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    --card-gradient: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
}

.vmi-theme-settings {
    --primary-color: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --bg-color: #f8fafc;
    --bg-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --card-gradient: linear-gradient(145deg, #ffffff 0%, #fcfdff 100%);
}

/* Page Background */
.vmi-theme-models,
.vmi-theme-videos,
.vmi-theme-tours,
.vmi-theme-main,
.vmi-theme-lms,
.vmi-theme-settings {
    background: var(--bg-gradient) !important;
    min-height: 100vh;
    padding: 20px;
    margin: -20px;
}

/* Page Title */
.vmi-theme-models h1,
.vmi-theme-videos h1,
.vmi-theme-tours h1,
.vmi-theme-main h1,
.vmi-theme-lms h1,
.vmi-theme-settings h1 {
    color: var(--primary-color) !important;
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    text-align: center;
    margin-bottom: 2rem !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Stats Grid */
.vmi-stats-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 1.5rem !important;
    margin: 2rem 0 !important;
}

.vmi-stat-card {
    background: var(--card-gradient) !important;
    border: 2px solid var(--primary-color) !important;
    border-radius: 16px !important;
    padding: 2rem !important;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.vmi-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 16px 16px 0 0;
}

.vmi-stat-card:hover {
    transform: translateY(-4px) !important;
    box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
}

.vmi-stat-card h3 {
    color: var(--primary-color) !important;
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    margin: 0 0 1rem 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.vmi-stat-card h3 .dashicons {
    background: var(--primary-color) !important;
    color: white !important;
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 18px !important;
}

.vmi-stat-number {
    font-size: 3rem !important;
    font-weight: 700 !important;
    color: var(--primary-color) !important;
    margin: 1rem 0 !important;
    line-height: 1 !important;
}

.vmi-stat-label {
    color: #6b7280 !important;
    font-size: 0.9rem !important;
    font-weight: 400 !important; font-size: 0.875rem !important;
    margin: 0 !important;
}

/* Quick Actions */
.vmi-quick-actions {
    display: flex !important;
    gap: 1rem !important;
    justify-content: center !important;
    margin: 2rem 0 !important;
    flex-wrap: wrap !important;
}

.vmi-quick-actions .button {
    padding: 1rem 2rem !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    border: none !important;
    font-size: 1rem !important;
    min-width: 200px !important;
    justify-content: center !important;
}

.vmi-quick-actions .button-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light)) !important;
    color: white !important;
    box-shadow: 0 4px 16px rgba(0,0,0,0.2) !important;
}

.vmi-quick-actions .button-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(0,0,0,0.3) !important;
}

.vmi-quick-actions .button:not(.button-primary) {
    background: white !important;
    color: var(--primary-color) !important;
    border: 2px solid var(--primary-color) !important;
}

.vmi-quick-actions .button:not(.button-primary):hover {
    background: var(--primary-color) !important;
    color: white !important;
    transform: translateY(-2px) !important;
}

/* Content Grid */
.vmi-content-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr)) !important;
    gap: 2rem !important;
    margin: 2rem 0 !important;
}

.vmi-content-card {
    background: var(--card-gradient) !important;
    border: 2px solid var(--primary-color) !important;
    border-radius: 16px !important;
    padding: 2rem !important;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.vmi-content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 16px 16px 0 0;
}

.vmi-content-card:hover {
    transform: translateY(-4px) !important;
    box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
}

.vmi-content-card h2 {
    color: var(--primary-color) !important;
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    margin: 0 0 1.5rem 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.vmi-content-card h2 .dashicons {
    background: var(--primary-color) !important;
    color: white !important;
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 18px !important;
}

/* Activity Items */
.vmi-activity-item {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
    padding: 1rem 0 !important;
    border-bottom: 1px solid #e5e7eb !important;
    transition: all 0.2s ease !important;
}

.vmi-activity-item:last-child {
    border-bottom: none !important;
}

.vmi-activity-item:hover {
    background: var(--bg-color) !important;
    margin: 0 -1rem !important;
    padding: 1rem !important;
    border-radius: 8px !important;
}

.vmi-activity-icon {
    width: 48px !important;
    height: 48px !important;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light)) !important;
    border-radius: 8px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
}

.vmi-activity-icon .dashicons {
    color: white !important;
    font-size: 20px !important;
}

.vmi-activity-content {
    flex: 1 !important;
    min-width: 0 !important;
}

.vmi-activity-title {
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    margin-bottom: 0.25rem !important;
}

.vmi-activity-title a {
    color: inherit !important;
    text-decoration: none !important;
    transition: color 0.2s ease !important;
}

.vmi-activity-title a:hover {
    color: var(--primary-color) !important;
}

.vmi-activity-meta {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    font-weight: 400 !important; font-size: 0.875rem !important;
}

/* Messages */
.vmi-warning-message {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%) !important;
    color: #dc2626 !important;
    padding: 0.75rem 1rem !important;
    border-radius: 8px !important;
    border-left: 3px solid #dc2626 !important;
    margin: 1rem 0 !important;
    font-weight: 400 !important; font-size: 0.875rem !important;
}

.vmi-info-message {
    background: var(--bg-color) !important;
    color: var(--primary-dark) !important;
    padding: 0.75rem 1rem !important;
    border-radius: 8px !important;
    border-left: 4px solid var(--primary-color) !important;
    margin: 1rem 0 !important;
    font-weight: 400 !important; font-size: 0.875rem !important;
}

/* Responsive Design */
@media screen and (max-width: 782px) {
    .vmi-stats-grid {
        grid-template-columns: 1fr !important;
    }
    
    .vmi-content-grid {
        grid-template-columns: 1fr !important;
    }
    
    .vmi-theme-models h1,
    .vmi-theme-videos h1,
    .vmi-theme-tours h1 {
        font-size: 2rem !important;
    }
    
    .vmi-stat-number {
        font-size: 2.5rem !important;
    }
    
    .vmi-quick-actions {
        flex-direction: column !important;
        align-items: center !important;
    }
}

/* Settings Page Styles */
.vmi-theme-settings .vmi-settings-content {
    display: grid !important;
    grid-template-columns: 1fr 300px !important;
    gap: 2rem !important;
    margin-top: 2rem !important;
}

.vmi-theme-settings .vmi-settings-nav {
    margin-bottom: 0 !important;
    border-bottom: 1px solid var(--primary-light) !important;
    background: var(--card-gradient) !important;
    border-radius: 12px 12px 0 0 !important;
    padding: 0 !important;
}

.vmi-theme-settings .vmi-settings-nav .nav-tab {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    color: #64748b !important;
    font-weight: 400 !important; font-size: 0.875rem !important;
    padding: 0.75rem 1rem !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
    position: relative !important;
}

.vmi-theme-settings .vmi-settings-nav .nav-tab:first-child {
    border-radius: 12px 0 0 0 !important;
}

.vmi-theme-settings .vmi-settings-nav .nav-tab:last-child {
    border-radius: 0 12px 0 0 !important;
}

.vmi-theme-settings .vmi-settings-nav .nav-tab:hover {
    background: rgba(255, 255, 255, 0.7) !important;
    color: var(--primary-dark) !important;
}

.vmi-theme-settings .vmi-settings-nav .nav-tab-active {
    background: var(--primary-color) !important;
    color: white !important;
    font-weight: 600 !important;
}

.vmi-theme-settings .vmi-settings-sidebar {
    display: flex !important;
    flex-direction: column !important;
    gap: 1.5rem !important;
}

.vmi-theme-settings .vmi-system-info {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.75rem !important;
}

.vmi-theme-settings .vmi-system-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 0.5rem 0 !important;
    border-bottom: 1px solid #e2e8f0 !important;
}

.vmi-theme-settings .vmi-system-item:last-child {
    border-bottom: none !important;
}

.vmi-theme-settings .vmi-system-item .label {
    font-weight: 400 !important; font-size: 0.875rem !important;
    color: #64748b !important;
}

.vmi-theme-settings .vmi-system-item .value {
    font-weight: 600 !important;
    color: var(--primary-dark) !important;
}

.vmi-theme-settings .vmi-quick-links {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
}

.vmi-theme-settings .vmi-quick-link {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    padding: 0.75rem !important;
    background: var(--card-gradient) !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 8px !important;
    color: #64748b !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

.vmi-theme-settings .vmi-quick-link:hover {
    background: var(--primary-color) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.vmi-theme-settings .vmi-quick-link .dashicons {
    font-size: 18px !important;
}

@media (max-width: 1024px) {
    .vmi-theme-settings .vmi-settings-content {
        grid-template-columns: 1fr !important;
    }
    
    .vmi-theme-settings .vmi-settings-sidebar {
        order: -1 !important;
    }
}

/* Ensure override of WordPress defaults */
.wrap.vmi-theme-models,
.wrap.vmi-theme-videos,
.wrap.vmi-theme-tours,
.wrap.vmi-theme-main,
.wrap.vmi-theme-lms,
.wrap.vmi-theme-settings {
    background: var(--bg-gradient) !important;
}

.wrap.vmi-theme-models *,
.wrap.vmi-theme-videos *,
.wrap.vmi-theme-tours *,
.wrap.vmi-theme-main *,
.wrap.vmi-theme-lms *,
.wrap.vmi-theme-settings * {
    box-sizing: border-box !important;
}

