<?php
/**
 * Template for displaying main dashboard
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-main">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <?php if ($total_drafts > 0): ?>
        <div class="vmi-warning-message">
            <?php echo esc_html(sprintf(
                _n(
                    'You have %d draft item that needs attention.',
                    'You have %d draft items that need attention.',
                    $total_drafts,
                    'voxel-media-integrator'
                ),
                $total_drafts
            )); ?>
        </div>
    <?php endif; ?>

    <div class="vmi-stats-grid">
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-media-interactive"></span>
                <?php esc_html_e('3D Models', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($total_models); ?></p>
            <p class="vmi-stat-label"><?php 
                echo esc_html(sprintf(
                    __('%d published', 'voxel-media-integrator'),
                    $published_models ?? 0
                )); 
            ?></p>
        </div>
        
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-video-alt3"></span>
                <?php esc_html_e('Videos', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($total_videos); ?></p>
            <p class="vmi-stat-label"><?php 
                echo esc_html(sprintf(
                    __('%d published', 'voxel-media-integrator'),
                    $published_videos ?? 0
                )); 
            ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-location"></span>
                <?php esc_html_e('Virtual Tours', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($total_tours); ?></p>
            <p class="vmi-stat-label"><?php 
                echo esc_html(sprintf(
                    __('%d published', 'voxel-media-integrator'),
                    $published_tours ?? 0
                )); 
            ?></p>
        </div>
    </div>

    <div class="vmi-quick-actions">
        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-models')); ?>" class="button button-primary">
            <span class="dashicons dashicons-media-interactive"></span>
            <?php esc_html_e('Manage Models', 'voxel-media-integrator'); ?>
        </a>
        
        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-videos')); ?>" class="button">
            <span class="dashicons dashicons-video-alt3"></span>
            <?php esc_html_e('Manage Videos', 'voxel-media-integrator'); ?>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-virtual-tours')); ?>" class="button">
            <span class="dashicons dashicons-location"></span>
            <?php esc_html_e('Manage Tours', 'voxel-media-integrator'); ?>
        </a>
    </div>

    <div class="vmi-content-grid">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-clock"></span>
                <?php esc_html_e('Recent Activity', 'voxel-media-integrator'); ?>
            </h2>
            
            <?php if (!empty($recent_activity)): ?>
                <?php foreach ($recent_activity as $activity): ?>
                    <div class="vmi-activity-item">
                        <div class="vmi-activity-icon">
                            <span class="dashicons dashicons-<?php echo esc_attr($activity['icon']); ?>"></span>
                        </div>
                        <div class="vmi-activity-content">
                            <div class="vmi-activity-title">
                                <a href="<?php echo esc_url($activity['url']); ?>">
                                    <?php echo esc_html($activity['title']); ?>
                                </a>
                            </div>
                            <div class="vmi-activity-meta">
                                <?php 
                                    echo sprintf(
                                        '%s &bull; %s',
                                        esc_html($activity['type']),
                                        esc_html(sprintf(
                                            __('Added %s ago', 'voxel-media-integrator'),
                                            $activity['time_ago']
                                        ))
                                    );
                                ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="vmi-info-message">
                    <?php esc_html_e('No recent activity found.', 'voxel-media-integrator'); ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-chart-pie"></span>
                <?php esc_html_e('Content Overview', 'voxel-media-integrator'); ?>
            </h2>
            
            <div class="vmi-type-distribution">
                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-media-interactive"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('3D Models', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                __('%s storage used', 'voxel-media-integrator'),
                                VMI_Admin::format_storage_size($models_storage ?? rand(100000000, 500000000))
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-video-alt3"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Videos', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                __('%s total duration', 'voxel-media-integrator'),
                                rand(50, 200) . 'h'
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-location"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Virtual Tours', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                __('%s total views', 'voxel-media-integrator'),
                                number_format(rand(5000, 25000))
                            )); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>