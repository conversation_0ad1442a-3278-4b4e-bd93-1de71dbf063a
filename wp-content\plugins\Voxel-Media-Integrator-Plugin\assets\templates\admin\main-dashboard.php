<?php
/**
 * Template for displaying main dashboard
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-main">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <?php if ($total_draft > 0 || $total_pending > 0): ?>
        <div class="vmi-warning-message">
            <?php
            $draft_text = '';
            $pending_text = '';

            if ($total_draft > 0) {
                $draft_text = sprintf(
                    _n('%d draft', '%d drafts', $total_draft, 'voxel-media-integrator'),
                    $total_draft
                );
            }

            if ($total_pending > 0) {
                $pending_text = sprintf(
                    _n('%d pending review', '%d pending review', $total_pending, 'voxel-media-integrator'),
                    $total_pending
                );
            }

            $messages = array_filter([$draft_text, $pending_text]);
            echo esc_html(sprintf(
                __('You have %s that need attention.', 'voxel-media-integrator'),
                implode(' and ', $messages)
            ));
            ?>
        </div>
    <?php endif; ?>

    <div class="vmi-stats-grid">
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-media-default"></span>
                <?php esc_html_e('Total Media', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($total_published); ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('Published items', 'voxel-media-integrator'); ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-visibility"></span>
                <?php esc_html_e('Virtual Tours', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_tours); ?></p>
            <p class="vmi-stat-label"><?php
                if ($draft_tours > 0 || $pending_tours > 0) {
                    echo esc_html(sprintf(
                        __('%d draft, %d pending', 'voxel-media-integrator'),
                        $draft_tours, $pending_tours
                    ));
                } else {
                    esc_html_e('All published', 'voxel-media-integrator');
                }
            ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-media-interactive"></span>
                <?php esc_html_e('3D Models', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_models); ?></p>
            <p class="vmi-stat-label"><?php
                if ($draft_models > 0 || $pending_models > 0) {
                    echo esc_html(sprintf(
                        __('%d draft, %d pending', 'voxel-media-integrator'),
                        $draft_models, $pending_models
                    ));
                } else {
                    esc_html_e('All published', 'voxel-media-integrator');
                }
            ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-video-alt3"></span>
                <?php esc_html_e('Videos', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_videos); ?></p>
            <p class="vmi-stat-label"><?php
                if ($draft_videos > 0 || $pending_videos > 0) {
                    echo esc_html(sprintf(
                        __('%d draft, %d pending', 'voxel-media-integrator'),
                        $draft_videos, $pending_videos
                    ));
                } else {
                    esc_html_e('All published', 'voxel-media-integrator');
                }
            ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-welcome-learn-more"></span>
                <?php esc_html_e('LMS Content', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_courses + $published_lessons + $published_quizzes); ?></p>
            <p class="vmi-stat-label"><?php
                echo esc_html(sprintf(
                    __('%d courses, %d lessons, %d quizzes', 'voxel-media-integrator'),
                    $published_courses, $published_lessons, $published_quizzes
                ));
            ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-chart-area"></span>
                <?php esc_html_e('Storage Used', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html(size_format($total_storage)); ?></p>
            <p class="vmi-stat-label"><?php
                $percentage = ($total_storage / $storage_limit) * 100;
                echo esc_html(sprintf(
                    __('%.1f%% of limit', 'voxel-media-integrator'),
                    $percentage
                ));
            ?></p>
        </div>
    </div>

    <div class="vmi-quick-actions">
        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-models')); ?>" class="button button-primary">
            <span class="dashicons dashicons-media-interactive"></span>
            <?php esc_html_e('Manage Models', 'voxel-media-integrator'); ?>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-videos')); ?>" class="button">
            <span class="dashicons dashicons-video-alt3"></span>
            <?php esc_html_e('Manage Videos', 'voxel-media-integrator'); ?>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-virtual-tours')); ?>" class="button">
            <span class="dashicons dashicons-location"></span>
            <?php esc_html_e('Manage Tours', 'voxel-media-integrator'); ?>
        </a>
    </div>

    <div class="vmi-content-grid">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-clock"></span>
                <?php esc_html_e('Recent Media', 'voxel-media-integrator'); ?>
            </h2>

            <?php if (!empty($recent_media)): ?>
                <?php foreach ($recent_media as $media):
                    $post_type = $media->post_type;
                    $icon = 'media-default';
                    $type_label = 'Media';

                    switch ($post_type) {
                        case 'wpvr_item':
                            $icon = 'visibility';
                            $type_label = 'Virtual Tour';
                            break;
                        case 'vmi_3d_models':
                            $icon = 'media-interactive';
                            $type_label = '3D Model';
                            break;
                        case 'vmi_videos':
                            $icon = 'video-alt3';
                            $type_label = 'Video';
                            break;
                        case 'vmi_courses':
                            $icon = 'welcome-learn-more';
                            $type_label = 'Course';
                            break;
                    }

                    $edit_url = admin_url("post.php?post={$media->ID}&action=edit");
                    $time_ago = human_time_diff(strtotime($media->post_date), current_time('timestamp'));
                ?>
                    <div class="vmi-activity-item">
                        <div class="vmi-activity-icon">
                            <span class="dashicons dashicons-<?php echo esc_attr($icon); ?>"></span>
                        </div>
                        <div class="vmi-activity-content">
                            <div class="vmi-activity-title">
                                <a href="<?php echo esc_url($edit_url); ?>">
                                    <?php echo esc_html($media->post_title); ?>
                                </a>
                            </div>
                            <div class="vmi-activity-meta">
                                <?php
                                    echo sprintf(
                                        '%s &bull; %s',
                                        esc_html($type_label),
                                        esc_html(sprintf(
                                            __('Added %s ago', 'voxel-media-integrator'),
                                            $time_ago
                                        ))
                                    );
                                ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="vmi-info-message">
                    <?php esc_html_e('No recent media found.', 'voxel-media-integrator'); ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-chart-pie"></span>
                <?php esc_html_e('Storage Breakdown', 'voxel-media-integrator'); ?>
            </h2>

            <div class="vmi-type-distribution">
                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-visibility"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Virtual Tours', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                __('%s storage used', 'voxel-media-integrator'),
                                size_format($tours_storage)
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-media-interactive"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('3D Models', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                __('%s storage used', 'voxel-media-integrator'),
                                size_format($models_storage)
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-video-alt3"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Videos', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                __('%s storage used', 'voxel-media-integrator'),
                                size_format($videos_storage)
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-welcome-learn-more"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('LMS Content', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                __('%s storage used', 'voxel-media-integrator'),
                                size_format($courses_storage)
                            )); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>