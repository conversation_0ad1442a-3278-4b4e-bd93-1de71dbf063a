<?php
/**
 * Template for displaying videos dashboard
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-videos">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <?php if ($draft_videos > 0): ?>
        <div class="vmi-warning-message">
            <?php echo esc_html(sprintf(
                _n(
                    'You have %d draft video that needs attention.',
                    'You have %d draft videos that need attention.',
                    $draft_videos,
                    'voxel-media-integrator'
                ),
                $draft_videos
            )); ?>
        </div>
    <?php endif; ?>

    <div class="vmi-stats-grid">
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-video-alt3"></span>
                <?php esc_html_e('Published Videos', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_videos); ?></p>
            <p class="vmi-stat-label"><?php
                echo esc_html(sprintf(
                    __('%d in review', 'voxel-media-integrator'),
                    $pending_videos ?? 0
                ));
            ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-format-video"></span>
                <?php esc_html_e('Total Videos', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($total_videos); ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('All video content', 'voxel-media-integrator'); ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-chart-area"></span>
                <?php esc_html_e('Watch Time', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php
                $total_duration = rand(100, 999); // Mock data - replace with actual
                echo esc_html($total_duration . 'h');
            ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('Total content duration', 'voxel-media-integrator'); ?></p>
        </div>
    </div>

    <div class="vmi-quick-actions">
        <a href="<?php echo esc_url(admin_url('post-new.php?post_type=' . VMI_Videos::CPT_SLUG)); ?>" class="button button-primary">
            <span class="dashicons dashicons-plus-alt2"></span>
            <?php esc_html_e('Add New Video', 'voxel-media-integrator'); ?>
        </a>

        <a href="<?php echo esc_url(admin_url('edit.php?post_type=' . VMI_Videos::CPT_SLUG)); ?>" class="button">
            <span class="dashicons dashicons-list-view"></span>
            <?php esc_html_e('View All Videos', 'voxel-media-integrator'); ?>
        </a>

        <a href="#vmi-video-settings" class="button vmi-settings-toggle">
            <span class="dashicons dashicons-admin-generic"></span>
            <?php esc_html_e('Video Settings', 'voxel-media-integrator'); ?>
        </a>
    </div>

    <!-- Video Settings Panel -->
    <div id="vmi-video-settings" class="vmi-settings-panel" style="display: none;">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-admin-generic"></span>
                <?php esc_html_e('Video Settings', 'voxel-media-integrator'); ?>
            </h2>

            <form method="post" action="options.php" class="vmi-settings-form">
                <?php
                settings_fields('vmi_video_settings');
                $video_settings = get_option('vmi_video_settings', array());
                ?>

                <div class="vmi-settings-grid">
                    <div class="vmi-setting-group">
                        <h3><?php esc_html_e('Playback Settings', 'voxel-media-integrator'); ?></h3>

                        <div class="vmi-setting-item">
                            <label for="vmi_autoplay">
                                <input type="checkbox" id="vmi_autoplay" name="vmi_video_settings[autoplay]" value="1"
                                    <?php checked(isset($video_settings['autoplay']) ? $video_settings['autoplay'] : 0, 1); ?>>
                                <?php esc_html_e('Enable Autoplay', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Videos will start playing automatically when loaded.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_loop">
                                <input type="checkbox" id="vmi_loop" name="vmi_video_settings[loop]" value="1"
                                    <?php checked(isset($video_settings['loop']) ? $video_settings['loop'] : 0, 1); ?>>
                                <?php esc_html_e('Enable Loop', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Videos will restart automatically when they end.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_muted">
                                <input type="checkbox" id="vmi_muted" name="vmi_video_settings[muted]" value="1"
                                    <?php checked(isset($video_settings['muted']) ? $video_settings['muted'] : 0, 1); ?>>
                                <?php esc_html_e('Start Muted', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Videos will start with audio muted.', 'voxel-media-integrator'); ?></p>
                        </div>
                    </div>

                    <div class="vmi-setting-group">
                        <h3><?php esc_html_e('Display Settings', 'voxel-media-integrator'); ?></h3>

                        <div class="vmi-setting-item">
                            <label for="vmi_controls">
                                <input type="checkbox" id="vmi_controls" name="vmi_video_settings[controls]" value="1"
                                    <?php checked(isset($video_settings['controls']) ? $video_settings['controls'] : 1, 1); ?>>
                                <?php esc_html_e('Show Controls', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Display video player controls (play, pause, volume, etc.).', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_preload"><?php esc_html_e('Preload Setting', 'voxel-media-integrator'); ?></label>
                            <select id="vmi_preload" name="vmi_video_settings[preload]">
                                <option value="none" <?php selected(isset($video_settings['preload']) ? $video_settings['preload'] : 'metadata', 'none'); ?>>
                                    <?php esc_html_e('None', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="metadata" <?php selected(isset($video_settings['preload']) ? $video_settings['preload'] : 'metadata', 'metadata'); ?>>
                                    <?php esc_html_e('Metadata Only', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="auto" <?php selected(isset($video_settings['preload']) ? $video_settings['preload'] : 'metadata', 'auto'); ?>>
                                    <?php esc_html_e('Auto (Full Video)', 'voxel-media-integrator'); ?>
                                </option>
                            </select>
                            <p class="description"><?php esc_html_e('How much of the video to preload when the page loads.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_poster_enabled">
                                <input type="checkbox" id="vmi_poster_enabled" name="vmi_video_settings[poster_enabled]" value="1"
                                    <?php checked(isset($video_settings['poster_enabled']) ? $video_settings['poster_enabled'] : 1, 1); ?>>
                                <?php esc_html_e('Enable Poster Images', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Show thumbnail images before video playback starts.', 'voxel-media-integrator'); ?></p>
                        </div>
                    </div>

                    <div class="vmi-setting-group">
                        <h3><?php esc_html_e('Quality & Performance', 'voxel-media-integrator'); ?></h3>

                        <div class="vmi-setting-item">
                            <label for="vmi_default_quality"><?php esc_html_e('Default Quality', 'voxel-media-integrator'); ?></label>
                            <select id="vmi_default_quality" name="vmi_video_settings[default_quality]">
                                <option value="auto" <?php selected(isset($video_settings['default_quality']) ? $video_settings['default_quality'] : 'auto', 'auto'); ?>>
                                    <?php esc_html_e('Auto', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="1080p" <?php selected(isset($video_settings['default_quality']) ? $video_settings['default_quality'] : 'auto', '1080p'); ?>>
                                    <?php esc_html_e('1080p (Full HD)', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="720p" <?php selected(isset($video_settings['default_quality']) ? $video_settings['default_quality'] : 'auto', '720p'); ?>>
                                    <?php esc_html_e('720p (HD)', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="480p" <?php selected(isset($video_settings['default_quality']) ? $video_settings['default_quality'] : 'auto', '480p'); ?>>
                                    <?php esc_html_e('480p (SD)', 'voxel-media-integrator'); ?>
                                </option>
                            </select>
                            <p class="description"><?php esc_html_e('Default video quality for playback.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_adaptive_streaming">
                                <input type="checkbox" id="vmi_adaptive_streaming" name="vmi_video_settings[adaptive_streaming]" value="1"
                                    <?php checked(isset($video_settings['adaptive_streaming']) ? $video_settings['adaptive_streaming'] : 1, 1); ?>>
                                <?php esc_html_e('Enable Adaptive Streaming', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Automatically adjust video quality based on connection speed.', 'voxel-media-integrator'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="vmi-settings-actions">
                    <?php submit_button(__('Save Video Settings', 'voxel-media-integrator'), 'primary', 'submit', false); ?>
                    <button type="button" class="button vmi-settings-toggle"><?php esc_html_e('Cancel', 'voxel-media-integrator'); ?></button>
                </div>
            </form>
        </div>
    </div>

    <div class="vmi-content-grid">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-clock"></span>
                <?php esc_html_e('Recent Videos', 'voxel-media-integrator'); ?>
            </h2>

            <?php if (!empty($recent_videos)): ?>
                <?php foreach ($recent_videos as $video): ?>
                    <div class="vmi-activity-item">
                        <div class="vmi-activity-icon">
                            <span class="dashicons dashicons-video-alt3"></span>
                        </div>
                        <div class="vmi-activity-content">
                            <div class="vmi-activity-title">
                                <a href="<?php echo esc_url(get_edit_post_link($video->ID)); ?>">
                                    <?php echo esc_html($video->post_title); ?>
                                </a>
                            </div>
                            <div class="vmi-activity-meta">
                                <?php
                                    echo esc_html(sprintf(
                                        __('Added %s ago', 'voxel-media-integrator'),
                                        human_time_diff(get_post_time('U', false, $video), current_time('timestamp'))
                                    ));
                                ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="vmi-info-message">
                    <?php esc_html_e('No recent videos found.', 'voxel-media-integrator'); ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-chart-pie"></span>
                <?php esc_html_e('Video Sources', 'voxel-media-integrator'); ?>
            </h2>

            <div class="vmi-type-distribution">
                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-upload"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Direct Uploads', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d video', '%d videos', $direct_uploads ?? rand(5, 15), 'voxel-media-integrator'),
                                $direct_uploads ?? rand(5, 15)
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-external"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('External Sources', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d video', '%d videos', $external_sources ?? rand(3, 10), 'voxel-media-integrator'),
                                $external_sources ?? rand(3, 10)
                            )); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Video settings toggle functionality
    $('.vmi-settings-toggle').on('click', function(e) {
        e.preventDefault();
        const settingsPanel = $('#vmi-video-settings');

        if (settingsPanel.is(':visible')) {
            settingsPanel.slideUp(300);
        } else {
            settingsPanel.slideDown(300);
        }
    });

    // Auto-hide settings panel when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#vmi-video-settings, .vmi-settings-toggle').length) {
            $('#vmi-video-settings').slideUp(300);
        }
    });

    // Form validation and enhanced UX
    $('#vmi-video-settings form').on('submit', function(e) {
        // Add loading state
        const submitBtn = $(this).find('input[type="submit"]');
        const originalText = submitBtn.val();

        submitBtn.val('<?php esc_html_e('Saving...', 'voxel-media-integrator'); ?>').prop('disabled', true);

        // Re-enable after a delay (WordPress will handle the actual save)
        setTimeout(function() {
            submitBtn.val(originalText).prop('disabled', false);
        }, 2000);
    });

    // Interactive setting previews
    $('#vmi_autoplay').on('change', function() {
        const isChecked = $(this).is(':checked');
        const description = $(this).siblings('.description');

        if (isChecked) {
            description.html('<?php esc_html_e('⚠️ Note: Many browsers block autoplay with sound. Consider enabling "Start Muted" as well.', 'voxel-media-integrator'); ?>');
            description.css('color', '#f59e0b');
        } else {
            description.html('<?php esc_html_e('Videos will start playing automatically when loaded.', 'voxel-media-integrator'); ?>');
            description.css('color', '#6b7280');
        }
    });

    // Quality setting recommendations
    $('#vmi_default_quality').on('change', function() {
        const quality = $(this).val();
        const description = $(this).siblings('.description');

        switch(quality) {
            case '1080p':
                description.html('<?php esc_html_e('Best quality but requires good internet connection. May cause buffering on slower connections.', 'voxel-media-integrator'); ?>');
                break;
            case '720p':
                description.html('<?php esc_html_e('Good balance between quality and performance. Recommended for most users.', 'voxel-media-integrator'); ?>');
                break;
            case '480p':
                description.html('<?php esc_html_e('Lower quality but faster loading. Good for mobile users or slower connections.', 'voxel-media-integrator'); ?>');
                break;
            default:
                description.html('<?php esc_html_e('Automatically selects the best quality based on user\'s connection speed.', 'voxel-media-integrator'); ?>');
        }
    });

    // Preload setting guidance
    $('#vmi_preload').on('change', function() {
        const preload = $(this).val();
        const description = $(this).siblings('.description');

        switch(preload) {
            case 'none':
                description.html('<?php esc_html_e('Fastest page loading but slower video start. Good for pages with many videos.', 'voxel-media-integrator'); ?>');
                break;
            case 'metadata':
                description.html('<?php esc_html_e('Balanced approach. Loads video info but not content. Recommended for most sites.', 'voxel-media-integrator'); ?>');
                break;
            case 'auto':
                description.html('<?php esc_html_e('Fastest video start but slower page loading. Use only for single video pages.', 'voxel-media-integrator'); ?>');
                break;
        }
    });
});
</script>
