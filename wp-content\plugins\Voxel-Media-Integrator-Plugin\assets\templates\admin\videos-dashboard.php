<?php
/**
 * Template for displaying videos dashboard
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-videos">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <?php if ($draft_videos > 0): ?>
        <div class="vmi-warning-message">
            <?php echo esc_html(sprintf(
                _n(
                    'You have %d draft video that needs attention.',
                    'You have %d draft videos that need attention.',
                    $draft_videos,
                    'voxel-media-integrator'
                ),
                $draft_videos
            )); ?>
        </div>
    <?php endif; ?>

    <div class="vmi-stats-grid">
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-video-alt3"></span>
                <?php esc_html_e('Published Videos', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_videos); ?></p>
            <p class="vmi-stat-label"><?php 
                echo esc_html(sprintf(
                    __('%d in review', 'voxel-media-integrator'),
                    $pending_videos ?? 0
                )); 
            ?></p>
        </div>
        
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-format-video"></span>
                <?php esc_html_e('Total Videos', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($total_videos); ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('All video content', 'voxel-media-integrator'); ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-chart-area"></span>
                <?php esc_html_e('Watch Time', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php 
                $total_duration = rand(100, 999); // Mock data - replace with actual
                echo esc_html($total_duration . 'h');
            ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('Total content duration', 'voxel-media-integrator'); ?></p>
        </div>
    </div>

    <div class="vmi-quick-actions">
        <a href="<?php echo esc_url(admin_url('post-new.php?post_type=' . VMI_Videos::CPT_SLUG)); ?>" class="button button-primary">
            <span class="dashicons dashicons-plus-alt2"></span>
            <?php esc_html_e('Add New Video', 'voxel-media-integrator'); ?>
        </a>
        
        <a href="<?php echo esc_url(admin_url('edit.php?post_type=' . VMI_Videos::CPT_SLUG)); ?>" class="button">
            <span class="dashicons dashicons-list-view"></span>
            <?php esc_html_e('View All Videos', 'voxel-media-integrator'); ?>
        </a>
    </div>

    <div class="vmi-content-grid">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-clock"></span>
                <?php esc_html_e('Recent Videos', 'voxel-media-integrator'); ?>
            </h2>
            
            <?php if (!empty($recent_videos)): ?>
                <?php foreach ($recent_videos as $video): ?>
                    <div class="vmi-activity-item">
                        <div class="vmi-activity-icon">
                            <span class="dashicons dashicons-video-alt3"></span>
                        </div>
                        <div class="vmi-activity-content">
                            <div class="vmi-activity-title">
                                <a href="<?php echo esc_url(get_edit_post_link($video->ID)); ?>">
                                    <?php echo esc_html($video->post_title); ?>
                                </a>
                            </div>
                            <div class="vmi-activity-meta">
                                <?php 
                                    echo esc_html(sprintf(
                                        __('Added %s ago', 'voxel-media-integrator'),
                                        human_time_diff(get_post_time('U', false, $video), current_time('timestamp'))
                                    ));
                                ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="vmi-info-message">
                    <?php esc_html_e('No recent videos found.', 'voxel-media-integrator'); ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-chart-pie"></span>
                <?php esc_html_e('Video Sources', 'voxel-media-integrator'); ?>
            </h2>
            
            <div class="vmi-type-distribution">
                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-upload"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Direct Uploads', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d video', '%d videos', $direct_uploads ?? rand(5, 15), 'voxel-media-integrator'),
                                $direct_uploads ?? rand(5, 15)
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-external"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('External Sources', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d video', '%d videos', $external_sources ?? rand(3, 10), 'voxel-media-integrator'),
                                $external_sources ?? rand(3, 10)
                            )); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
