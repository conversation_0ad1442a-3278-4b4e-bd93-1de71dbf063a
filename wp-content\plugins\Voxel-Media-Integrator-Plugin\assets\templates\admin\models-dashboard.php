<?php
/**
 * Template for displaying 3D models dashboard
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-models">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <?php if ($draft_models > 0): ?>
        <div class="vmi-warning-message">
            <?php echo esc_html(sprintf(
                _n(
                    'You have %d draft 3D model that needs attention.',
                    'You have %d draft 3D models that need attention.',
                    $draft_models,
                    'voxel-media-integrator'
                ),
                $draft_models
            )); ?>
        </div>
    <?php endif; ?>

    <div class="vmi-stats-grid">
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-media-interactive"></span>
                <?php esc_html_e('Published Models', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_models); ?></p>
            <p class="vmi-stat-label"><?php 
                echo esc_html(sprintf(
                    __('%d in review', 'voxel-media-integrator'),
                    $pending_models
                )); 
            ?></p>
        </div>
        
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-admin-appearance"></span>
                <?php esc_html_e('Model Types', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($total_model_types); ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('Different formats supported', 'voxel-media-integrator'); ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-chart-area"></span>
                <?php esc_html_e('Storage Used', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php 
                echo wp_kses_post(VMI_Admin::format_storage_size($storage_used));
            ?></p>
            <p class="vmi-stat-label"><?php 
                echo esc_html(sprintf(
                    __('%s available', 'voxel-media-integrator'),
                    VMI_Admin::format_storage_size($storage_available)
                )); 
            ?></p>
        </div>
    </div>

    <div class="vmi-quick-actions">
        <a href="<?php echo esc_url(admin_url('post-new.php?post_type=' . VMI_Models::CPT_SLUG)); ?>" class="button button-primary">
            <span class="dashicons dashicons-plus-alt2"></span>
            <?php esc_html_e('Add New Model', 'voxel-media-integrator'); ?>
        </a>
        
        <a href="<?php echo esc_url(admin_url('edit.php?post_type=' . VMI_Models::CPT_SLUG)); ?>" class="button">
            <span class="dashicons dashicons-list-view"></span>
            <?php esc_html_e('View All Models', 'voxel-media-integrator'); ?>
        </a>
    </div>

    <div class="vmi-content-grid">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-clock"></span>
                <?php esc_html_e('Recent Models', 'voxel-media-integrator'); ?>
            </h2>
            
            <?php if (!empty($recent_models)): ?>
                <?php foreach ($recent_models as $model): ?>
                    <div class="vmi-activity-item">
                        <div class="vmi-activity-icon">
                            <span class="dashicons dashicons-media-interactive"></span>
                        </div>
                        <div class="vmi-activity-content">
                            <div class="vmi-activity-title">
                                <a href="<?php echo esc_url(get_edit_post_link($model->ID)); ?>">
                                    <?php echo esc_html($model->post_title); ?>
                                </a>
                            </div>
                            <div class="vmi-activity-meta">
                                <?php 
                                    $type = get_post_meta($model->ID, 'vmi_model_type', true);
                                    echo sprintf(
                                        '%s &bull; %s',
                                        esc_html(strtoupper($type)),
                                        esc_html(sprintf(
                                            __('Added %s ago', 'voxel-media-integrator'),
                                            human_time_diff(get_post_time('U', false, $model), current_time('timestamp'))
                                        ))
                                    );
                                ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="vmi-info-message">
                    <?php esc_html_e('No recent models found.', 'voxel-media-integrator'); ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-chart-pie"></span>
                <?php esc_html_e('Model Types Distribution', 'voxel-media-integrator'); ?>
            </h2>
            
            <div class="vmi-type-distribution">
                <?php foreach ($model_types_stats as $type => $count): ?>
                    <div class="vmi-activity-item">
                        <div class="vmi-activity-icon">
                            <span class="dashicons dashicons-media-interactive"></span>
                        </div>
                        <div class="vmi-activity-content">
                            <div class="vmi-activity-title">
                                <?php echo esc_html(strtoupper($type)); ?>
                            </div>
                            <div class="vmi-activity-meta">
                                <?php echo esc_html(sprintf(
                                    _n('%d model', '%d models', $count, 'voxel-media-integrator'),
                                    $count
                                )); ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>
