<?php
/**
 * Template for displaying templates dashboard with visual widget
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-main">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <!-- Template Categories Filter -->
    <div class="vmi-template-filters">
        <button class="vmi-filter-btn active" data-category="all">
            <span class="dashicons dashicons-admin-media"></span>
            <?php esc_html_e('All Templates', 'voxel-media-integrator'); ?>
        </button>
        <?php foreach ($template_categories as $category_key => $category): ?>
            <button class="vmi-filter-btn" data-category="<?php echo esc_attr($category_key); ?>" style="--category-color: <?php echo esc_attr($category['color']); ?>">
                <span class="dashicons dashicons-<?php echo esc_attr($category['icon']); ?>"></span>
                <?php echo esc_html($category['title']); ?>
            </button>
        <?php endforeach; ?>
    </div>

    <!-- Templates Grid -->
    <div class="vmi-templates-grid">
        <?php foreach ($templates_metadata as $template_key => $template): ?>
            <div class="vmi-template-card" data-category="<?php echo esc_attr($template['category']); ?>">
                <div class="vmi-template-preview">
                    <div class="vmi-template-image">
                        <?php 
                        $preview_url = plugin_dir_url(dirname(dirname(__FILE__))) . 'images/template-previews/' . $template['preview_image'];
                        $placeholder_color = $template_categories[$template['category']]['color'];
                        ?>
                        <div class="vmi-template-placeholder" style="background: linear-gradient(135deg, <?php echo esc_attr($placeholder_color); ?>20, <?php echo esc_attr($placeholder_color); ?>40);">
                            <span class="dashicons dashicons-<?php echo esc_attr($template_categories[$template['category']]['icon']); ?>" style="color: <?php echo esc_attr($placeholder_color); ?>;"></span>
                        </div>
                        <div class="vmi-template-overlay">
                            <button class="vmi-preview-btn" data-template="<?php echo esc_attr($template_key); ?>">
                                <span class="dashicons dashicons-visibility"></span>
                                <?php esc_html_e('Preview', 'voxel-media-integrator'); ?>
                            </button>
                        </div>
                    </div>
                    
                    <div class="vmi-template-category-badge" style="background: <?php echo esc_attr($template_categories[$template['category']]['color']); ?>;">
                        <span class="dashicons dashicons-<?php echo esc_attr($template_categories[$template['category']]['icon']); ?>"></span>
                        <?php echo esc_html($template_categories[$template['category']]['title']); ?>
                    </div>
                </div>

                <div class="vmi-template-content">
                    <h3 class="vmi-template-title"><?php echo esc_html($template['title']); ?></h3>
                    <p class="vmi-template-description"><?php echo esc_html($template['description']); ?></p>
                    
                    <div class="vmi-template-features">
                        <?php foreach ($template['features'] as $feature): ?>
                            <span class="vmi-feature-tag"><?php echo esc_html($feature); ?></span>
                        <?php endforeach; ?>
                    </div>

                    <div class="vmi-template-actions">
                        <div class="vmi-shortcode-container">
                            <input type="text" class="vmi-shortcode-input" value="<?php echo esc_attr($template['shortcode']); ?>" readonly>
                            <button class="vmi-copy-btn" data-shortcode="<?php echo esc_attr($template['shortcode']); ?>">
                                <span class="dashicons dashicons-admin-page"></span>
                                <?php esc_html_e('Copy', 'voxel-media-integrator'); ?>
                            </button>
                        </div>
                        
                        <div class="vmi-template-buttons">
                            <button class="button button-primary vmi-use-template" data-template="<?php echo esc_attr($template_key); ?>">
                                <span class="dashicons dashicons-plus-alt2"></span>
                                <?php esc_html_e('Use Template', 'voxel-media-integrator'); ?>
                            </button>
                            <button class="button vmi-customize-template" data-template="<?php echo esc_attr($template_key); ?>">
                                <span class="dashicons dashicons-admin-customizer"></span>
                                <?php esc_html_e('Customize', 'voxel-media-integrator'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Template Preview Modal -->
    <div id="vmi-template-modal" class="vmi-modal" style="display: none;">
        <div class="vmi-modal-content">
            <div class="vmi-modal-header">
                <h2 id="vmi-modal-title"><?php esc_html_e('Template Preview', 'voxel-media-integrator'); ?></h2>
                <button class="vmi-modal-close">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>
            </div>
            <div class="vmi-modal-body">
                <div id="vmi-template-preview-content">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
            <div class="vmi-modal-footer">
                <button class="button button-primary" id="vmi-modal-use-template">
                    <?php esc_html_e('Use This Template', 'voxel-media-integrator'); ?>
                </button>
                <button class="button" id="vmi-modal-customize">
                    <?php esc_html_e('Customize', 'voxel-media-integrator'); ?>
                </button>
            </div>
        </div>
    </div>

    <!-- Template Customizer Panel -->
    <div id="vmi-customizer-panel" class="vmi-customizer" style="display: none;">
        <div class="vmi-customizer-header">
            <h3><?php esc_html_e('Customize Template', 'voxel-media-integrator'); ?></h3>
            <button class="vmi-customizer-close">
                <span class="dashicons dashicons-no-alt"></span>
            </button>
        </div>
        <div class="vmi-customizer-content">
            <div class="vmi-customizer-options">
                <!-- Customization options will be loaded here -->
            </div>
            <div class="vmi-customizer-preview">
                <!-- Live preview will be shown here -->
            </div>
        </div>
        <div class="vmi-customizer-footer">
            <button class="button button-primary" id="vmi-save-customization">
                <?php esc_html_e('Generate Shortcode', 'voxel-media-integrator'); ?>
            </button>
            <button class="button" id="vmi-reset-customization">
                <?php esc_html_e('Reset', 'voxel-media-integrator'); ?>
            </button>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Template filtering
    $('.vmi-filter-btn').on('click', function() {
        const category = $(this).data('category');
        
        $('.vmi-filter-btn').removeClass('active');
        $(this).addClass('active');
        
        if (category === 'all') {
            $('.vmi-template-card').show();
        } else {
            $('.vmi-template-card').hide();
            $(`.vmi-template-card[data-category="${category}"]`).show();
        }
    });

    // Copy shortcode functionality
    $('.vmi-copy-btn').on('click', function() {
        const shortcode = $(this).data('shortcode');
        const input = $(this).siblings('.vmi-shortcode-input');
        
        input.select();
        document.execCommand('copy');
        
        $(this).find('.dashicons').removeClass('dashicons-admin-page').addClass('dashicons-yes');
        $(this).find('span:not(.dashicons)').text('<?php esc_html_e('Copied!', 'voxel-media-integrator'); ?>');
        
        setTimeout(() => {
            $(this).find('.dashicons').removeClass('dashicons-yes').addClass('dashicons-admin-page');
            $(this).find('span:not(.dashicons)').text('<?php esc_html_e('Copy', 'voxel-media-integrator'); ?>');
        }, 2000);
    });

    // Template preview
    $('.vmi-preview-btn').on('click', function() {
        const template = $(this).data('template');
        $('#vmi-template-modal').show();
        // Load preview content via AJAX
    });

    // Modal close
    $('.vmi-modal-close').on('click', function() {
        $('#vmi-template-modal').hide();
    });

    // Template customization
    $('.vmi-customize-template').on('click', function() {
        const template = $(this).data('template');
        $('#vmi-customizer-panel').show();
        // Load customization options via AJAX
    });

    // Customizer close
    $('.vmi-customizer-close').on('click', function() {
        $('#vmi-customizer-panel').hide();
    });
});
</script>
