<![CDATA[<?php
/**
 * Template for displaying virtual tours dashboard
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-tours">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <?php if ($draft_tours > 0): ?>
        <div class="vmi-warning-message">
            <?php echo esc_html(sprintf(
                _n(
                    'You have %d draft virtual tour that needs attention.',
                    'You have %d draft virtual tours that need attention.',
                    $draft_tours,
                    'voxel-media-integrator'
                ),
                $draft_tours
            )); ?>
        </div>
    <?php endif; ?>

    <div class="vmi-stats-grid">
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-location"></span>
                <?php esc_html_e('Published Tours', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_tours); ?></p>
            <p class="vmi-stat-label"><?php 
                echo esc_html(sprintf(
                    __('%d in review', 'voxel-media-integrator'),
                    $pending_tours ?? 0
                )); 
            ?></p>
        </div>
        
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-admin-site-alt3"></span>
                <?php esc_html_e('Total Tours', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($total_tours); ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('All virtual experiences', 'voxel-media-integrator'); ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-visibility"></span>
                <?php esc_html_e('Total Views', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php 
                $total_views = rand(1000, 9999); // Mock data - replace with actual
                echo esc_html(number_format($total_views));
            ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('Across all tours', 'voxel-media-integrator'); ?></p>
        </div>
    </div>

    <div class="vmi-quick-actions">
        <a href="<?php echo esc_url(admin_url('post-new.php?post_type=' . VMI_Virtual_Tours::CPT_SLUG)); ?>" class="button button-primary">
            <span class="dashicons dashicons-plus-alt2"></span>
            <?php esc_html_e('Add New Tour', 'voxel-media-integrator'); ?>
        </a>
        
        <a href="<?php echo esc_url(admin_url('edit.php?post_type=' . VMI_Virtual_Tours::CPT_SLUG)); ?>" class="button">
            <span class="dashicons dashicons-list-view"></span>
            <?php esc_html_e('View All Tours', 'voxel-media-integrator'); ?>
        </a>
        
        <a href="<?php echo esc_url(admin_url('edit-tags.php?taxonomy=vmi_media_category&post_type=' . VMI_Virtual_Tours::CPT_SLUG)); ?>" class="button">
            <span class="dashicons dashicons-tag"></span>
            <?php esc_html_e('Tour Categories', 'voxel-media-integrator'); ?>
        </a>
        
        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-virtual-tours-settings')); ?>" class="button">
            <span class="dashicons dashicons-admin-settings"></span>
            <?php esc_html_e('Tour Settings', 'voxel-media-integrator'); ?>
        </a>
    </div>

    <div class="vmi-content-grid">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-clock"></span>
                <?php esc_html_e('Recent Tours', 'voxel-media-integrator'); ?>
            </h2>
            
            <?php if (!empty($recent_tours)): ?>
                <?php foreach ($recent_tours as $tour): ?>
                    <div class="vmi-activity-item">
                        <div class="vmi-activity-icon">
                            <span class="dashicons dashicons-location"></span>
                        </div>
                        <div class="vmi-activity-content">
                            <div class="vmi-activity-title">
                                <a href="<?php echo esc_url(get_edit_post_link($tour->ID)); ?>">
                                    <?php echo esc_html($tour->post_title); ?>
                                </a>
                            </div>
                            <div class="vmi-activity-meta">
                                <?php 
                                    $tour_type = get_post_meta($tour->ID, 'vmi_tour_type', true);
                                    echo sprintf(
                                        '%s &bull; %s',
                                        esc_html(ucfirst($tour_type ?: 'Standard')),
                                        esc_html(sprintf(
                                            __('Added %s ago', 'voxel-media-integrator'),
                                            human_time_diff(get_post_time('U', false, $tour), current_time('timestamp'))
                                        ))
                                    );
                                ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="vmi-info-message">
                    <?php esc_html_e('No recent tours found.', 'voxel-media-integrator'); ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-chart-pie"></span>
                <?php esc_html_e('Tour Types Distribution', 'voxel-media-integrator'); ?>
            </h2>
            
            <div class="vmi-type-distribution">
                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-building"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Property Tours', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d tour', '%d tours', $property_tours ?? rand(3, 8), 'voxel-media-integrator'),
                                $property_tours ?? rand(3, 8)
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-art"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Gallery Tours', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d tour', '%d tours', $gallery_tours ?? rand(2, 6), 'voxel-media-integrator'),
                                $gallery_tours ?? rand(2, 6)
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-store"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Business Tours', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d tour', '%d tours', $business_tours ?? rand(1, 4), 'voxel-media-integrator'),
                                $business_tours ?? rand(1, 4)
                            )); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>]]>
