# Voxel Media Integrator Admin Page Refactor Plan

## 1. Goal

To restructure the Voxel Media Integrator admin pages to have a distinct main Dashboard page (summarizing all media types) and a separate, clearly defined Settings page (for plugin configurations), following WordPress best practices.

## 2. Current Problem

Currently, the main plugin page (`vmi-dashboard`), the "Dashboard" submenu, and the "Settings" submenu (`vmi-settings`) all use the same rendering function (`VMI_Admin->render_settings_page()`). This results in these pages displaying identical content, mixing dashboard/overview elements with plugin settings forms, which is not ideal for user experience or maintainability.

## 3. Proposed Structure

The refactor will create two distinct primary pages:

*   **Main Dashboard:** A comprehensive overview of all media types managed by the plugin.
*   **Settings Page:** A dedicated page for all plugin-wide configurations, using a tabbed interface for better organization.

### Proposed Menu Structure:

```mermaid
graph TD
    A[Media Integrator (vmi-dashboard) - render_main_dashboard_page] --> B{Submenus};
    %% C[Dashboard submenu - REMOVED or points to render_main_dashboard_page]
    B --> D[Virtual Tours (vmi-virtual-tours) - render_virtual_tours_dashboard_page];
    B --> E[LMS (vmi-lms) - render_lms_page];
    B --> F[Videos (vmi-videos) - render_video_page];
    B --> G[3D Models (vmi-3d-models) - render_3d_models_page];
    B --> H[Settings (vmi-settings) - render_plugin_settings_page];
```
*(Assumes the redundant "Dashboard" submenu is removed, with the top-level item serving as the main dashboard).*

## 4. Detailed Plan

### Phase 1: Create New Rendering Functions

1.  **`render_main_dashboard_page()`**
    *   **Source:** Based on the current `render_settings_page()` structure.
    *   **Key Changes:**
        *   Remove all settings form markup (e.g., `<form>`, `settings_fields()`, `do_settings_sections()`, `submit_button()`).
        *   Ensure it uses the generalized statistics logic to display counts for all media types (Virtual Tours, Videos, 3D Models, Courses, Lessons, Quizzes) and the total storage used.
        *   The "Recent Activity" section will initially remain simple (e.g., showing recent virtual tours or links to media management screens) due to the complexity of a fully unified activity feed.

2.  **`render_plugin_settings_page()`**
    *   **Purpose:** Will be dedicated to rendering all plugin settings.
    *   **Structure:**
        *   Standard page wrapper: `<div class="wrap"><h1>Plugin Settings</h1>...</div>`.
        *   WordPress Settings API form: `<form method="post" action="options.php">`.
        *   **Tabbed Interface:** Implement a tabbed navigation to organize different settings groups.
        *   Each tab will display the settings for a specific group by calling:
            *   `settings_fields('settings_group_for_tab');`
            *   `do_settings_sections('settings_group_for_tab');`
        *   **Settings Groups to Include (each in its own tab):**
            *   `vmi_settings` (General/Media Limits)
            *   `vmi_virtual_tours_settings`
            *   `vmi_3d_models_settings`
            *   `vmi_lms_settings`
            *   `vmi_video_settings`
        *   A single `submit_button()` for the form.

### Phase 2: Update Admin Menu Callbacks in `add_admin_menu()`

1.  **Top-level page ("Media Integrator", slug `vmi-dashboard`):**
    *   Change its callback from `array( $this, 'render_settings_page' )` to `array( $this, 'render_main_dashboard_page' )`.
2.  **"Dashboard" submenu (slug `vmi-dashboard`):**
    *   **Recommended:** Remove this submenu entirely, as the top-level item will now function as the main dashboard.
3.  **"Settings" submenu (slug `vmi-settings`):**
    *   Change its callback from `array( $this, 'render_settings_page' )` to `array( $this, 'render_plugin_settings_page' )`.

### Phase 3: Refine and Deprecate

1.  **Original `render_settings_page()`:** This function will no longer be directly called by the primary admin menu after the changes. It can be removed or kept as an empty/deprecated function during a transition period.

## 5. Visual Plan for Settings Page (Conceptual Tabs)

```
Plugin Settings
======================================================================
| General | Virtual Tours | 3D Models | LMS | Videos |
======================================================================

[Content for the active tab, e.g., General Settings (Media Limits)]
<form method="post" action="options.php">
    settings_fields('vmi_settings'); // Or the active tab's group
    do_settings_sections('vmi_settings'); // Or the active tab's group
    submit_button();
</form>
```

This plan aims to create a clear separation of concerns, improve user experience, and align with WordPress admin design best practices.