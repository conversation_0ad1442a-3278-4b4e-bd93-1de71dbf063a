<![CDATA[<?php
/**
 * Admin management class
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Class for managing admin interface
 */
class VMI_Admin {

    /**
     * Constructor
     */
    public function __construct() {
        // Add admin menu
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );

        // Register settings
        add_action( 'admin_init', array( $this, 'register_settings' ) );

        // Add AJAX handler for dismissing notices
        add_action( 'wp_ajax_vmi_dismiss_field_notice', array( $this, 'dismiss_field_notice' ) );

        // Enqueue admin scripts and styles
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Add main menu item
        add_menu_page(
            __( 'Voxel Media Integrator', 'voxel-media-integrator' ),
            __( 'Media Integrator', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-settings',
            array( $this, 'render_settings_page' ),
            'dashicons-format-gallery',
            30
        );

        // Add settings submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Settings', 'voxel-media-integrator' ),
            __( 'Settings', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-settings',
            array( $this, 'render_settings_page' )
        );

        // Add Virtual Tours Dashboard submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Virtual Tours', 'voxel-media-integrator' ),
            __( 'Virtual Tours', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-virtual-tours-dashboard',
            array( $this, 'render_virtual_tours_dashboard_page' )
        );

        // Add 3D models settings submenu
        add_submenu_page(
            'vmi-settings',
            __( '3D Models', 'voxel-media-integrator' ),
            __( '3D Models', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-3d-models',
            array( $this, 'render_3d_models_page' )
        );

        // Add LMS Dashboard submenu
        add_submenu_page(
            'vmi-settings',
            __( 'LMS Dashboard', 'voxel-media-integrator' ),
            __( 'LMS Dashboard', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-lms',
            array( $this, 'render_lms_page' )
        );



        // Add video dashboard submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Video Dashboard', 'voxel-media-integrator' ),
            __( 'Video Dashboard', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-video-settings',
            array( $this, 'render_video_settings_page' )
        );

        // Add video dashboard submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Video Settings', 'voxel-media-integrator' ),
            __( 'Video Settings', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-video-settings',
            array( $this, 'render_video_settings_page' )
        );



        // Add media dashboard submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Media Dashboard', 'voxel-media-integrator' ),
            __( 'Media Dashboard', 'voxel-media-integrator' ),
            'edit_posts',
            'vmi-media-dashboard',
            array( $this, 'render_media_dashboard_page' )
        );

        // Add templates submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Templates', 'voxel-media-integrator' ),
            __( 'Templates', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-templates',
            array( $this, 'render_templates_page' )
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        // Register general settings
        register_setting( 'vmi_settings', 'vmi_media_limits', array(
            'type' => 'array',
            'sanitize_callback' => array( $this, 'sanitize_media_limits' ),
        ) );

        // Virtual Tours settings are now registered in the VMI_Virtual_Tours class

        // Register 3D models settings
        register_setting( 'vmi_3d_models_settings', 'vmi_3d_models_settings', array(
            'type' => 'array',
            'sanitize_callback' => array( $this, 'sanitize_3d_models_settings' ),
        ) );

        // Register LMS settings
        register_setting( 'vmi_lms_settings', 'vmi_lms_settings', array(
            'type' => 'array',
            'sanitize_callback' => array( $this, 'sanitize_lms_settings' ),
        ) );

        // Register video settings
        register_setting( 'vmi_video_settings', 'vmi_video_settings', array(
            'type' => 'array',
            'sanitize_callback' => array( $this, 'sanitize_video_settings' ),
        ) );

        // Add general settings section
        add_settings_section(
            'vmi_limits_section',
            __( 'Media Limits', 'voxel-media-integrator' ),
            array( $this, 'render_limits_section' ),
            'vmi_settings'
        );

        // Add settings fields
        add_settings_field(
            'vmi_free_limit',
            __( 'Free User Limit', 'voxel-media-integrator' ),
            array( $this, 'render_free_limit_field' ),
            'vmi_settings',
            'vmi_limits_section'
        );

        add_settings_field(
            'vmi_basic_limit',
            __( 'Basic Plan Limit', 'voxel-media-integrator' ),
            array( $this, 'render_basic_limit_field' ),
            'vmi_settings',
            'vmi_limits_section'
        );

        add_settings_field(
            'vmi_premium_limit',
            __( 'Premium Plan Limit', 'voxel-media-integrator' ),
            array( $this, 'render_premium_limit_field' ),
            'vmi_settings',
            'vmi_limits_section'
        );
    }

    // Virtual Tours settings sanitization is now handled by the VMI_Virtual_Tours class

    /**
     * Sanitize 3D models settings
     *
     * @param array $input Input values.
     * @return array Sanitized values.
     */
    public function sanitize_3d_models_settings( $input ) {
        $output = array();

        // Sanitize checkboxes
        $checkboxes = array(
            'format_glb',
            'format_gltf',
            'format_obj',
            'format_fbx',
            'format_dae',
            'format_stl',
            'auto_rotate',
            'enable_timeline',
        );

        foreach ( $checkboxes as $checkbox ) {
            $output[ $checkbox ] = isset( $input[ $checkbox ] ) ? 1 : 0;
        }

        // Sanitize numeric fields
        $numeric_fields = array(
            'default_model_height',
            'max_upload_size',
        );

        foreach ( $numeric_fields as $field ) {
            $output[ $field ] = isset( $input[ $field ] ) ? absint( $input[ $field ] ) : 0;
        }

        // Sanitize select fields
        $output['default_model_viewer'] = isset( $input['default_model_viewer'] ) && in_array( $input['default_model_viewer'], array( 'model-viewer', 'three' ) ) ? $input['default_model_viewer'] : 'model-viewer';

        return $output;
    }

    /**
     * Sanitize LMS settings
     *
     * @param array $input Input values.
     * @return array Sanitized values.
     */
    public function sanitize_lms_settings( $input ) {
        $output = array();

        // Sanitize checkboxes
        $checkboxes = array(
            'enable_tutor_lms',
            'enable_categories',
            'enable_pricing',
            'enable_business',
            'enable_timeline',
        );

        foreach ( $checkboxes as $checkbox ) {
            $output[ $checkbox ] = isset( $input[ $checkbox ] ) ? 1 : 0;
        }

        // Sanitize select fields
        $output['course_creation_role'] = isset( $input['course_creation_role'] ) && in_array( $input['course_creation_role'], array( 'administrator', 'editor', 'author', 'contributor', 'subscriber' ) ) ? $input['course_creation_role'] : 'administrator';
        $output['course_moderation'] = isset( $input['course_moderation'] ) && in_array( $input['course_moderation'], array( 'publish', 'pending' ) ) ? $input['course_moderation'] : 'pending';

        // Sanitize numeric fields
        $output['max_courses'] = isset( $input['max_courses'] ) ? absint( $input['max_courses'] ) : 0;

        // Sanitize text fields
        $output['business_field_id'] = isset( $input['business_field_id'] ) ? sanitize_text_field( $input['business_field_id'] ) : '';

        return $output;
    }

    /**
     * Sanitize video settings
     *
     * @param array $input Input values.
     * @return array Sanitized values.
     */
    public function sanitize_video_settings( $input ) {
        $output = array();

        // Sanitize checkboxes
        $checkboxes = array(
            'enable_uploads',
            'enable_embedding',
            'format_mp4',
            'format_webm',
            'format_ogg',
            'format_mov',
            'embed_youtube',
            'embed_vimeo',
            'embed_dailymotion',
            'embed_facebook',
            'embed_twitch',
            'enable_business',
            'enable_timeline',
            // New video dashboard settings
            'autoplay',
            'loop',
            'muted',
            'controls',
            'poster_enabled',
            'adaptive_streaming',
        );

        foreach ( $checkboxes as $checkbox ) {
            $output[ $checkbox ] = isset( $input[ $checkbox ] ) ? 1 : 0;
        }

        // Sanitize select fields
        $output['video_moderation'] = isset( $input['video_moderation'] ) && in_array( $input['video_moderation'], array( 'publish', 'pending' ) ) ? $input['video_moderation'] : 'pending';

        // New video dashboard select fields
        $output['preload'] = isset( $input['preload'] ) && in_array( $input['preload'], array( 'none', 'metadata', 'auto' ) ) ? $input['preload'] : 'metadata';
        $output['default_quality'] = isset( $input['default_quality'] ) && in_array( $input['default_quality'], array( 'auto', '1080p', '720p', '480p' ) ) ? $input['default_quality'] : 'auto';

        // Sanitize numeric fields
        $output['max_upload_size'] = isset( $input['max_upload_size'] ) ? absint( $input['max_upload_size'] ) : 0;
        $output['default_video_height'] = isset( $input['default_video_height'] ) ? absint( $input['default_video_height'] ) : 0;

        // Sanitize text fields
        $output['business_field_id'] = isset( $input['business_field_id'] ) ? sanitize_text_field( $input['business_field_id'] ) : '';

        return $output;
    }

    /**
     * Sanitize media limits
     *
     * @param array $input Input values.
     * @return array Sanitized values.
     */
    public function sanitize_media_limits( $input ) {
        $output = array();

        // Sanitize numeric fields
        $numeric_fields = array(
            'free_limit',
            'basic_limit',
            'premium_limit',
        );

        foreach ( $numeric_fields as $field ) {
            $output[ $field ] = isset( $input[ $field ] ) ? absint( $input[ $field ] ) : 0;
        }

        return $output;
    }

    /**
     * Render limits section
     */
    public function render_limits_section() {
        echo '<p>' . esc_html__( 'Set the media upload limits for different user plans.', 'voxel-media-integrator' ) . '</p>';
    }

    /**
     * Render free limit field
     */
    public function render_free_limit_field() {
        $options = get_option( 'vmi_media_limits', array() );
        $free_limit = isset( $options['free_limit'] ) ? $options['free_limit'] : 1;
        ?>
        <input type="number" name="vmi_media_limits[free_limit]" value="<?php echo esc_attr( $free_limit ); ?>" min="0" />
        <p class="description"><?php esc_html_e( 'Maximum number of media items for free users.', 'voxel-media-integrator' ); ?></p>
        <?php
    }

    /**
     * Render basic limit field
     */
    public function render_basic_limit_field() {
        $options = get_option( 'vmi_media_limits', array() );
        $basic_limit = isset( $options['basic_limit'] ) ? $options['basic_limit'] : 5;
        ?>
        <input type="number" name="vmi_media_limits[basic_limit]" value="<?php echo esc_attr( $basic_limit ); ?>" min="0" />
        <p class="description"><?php esc_html_e( 'Maximum number of media items for basic plan users.', 'voxel-media-integrator' ); ?></p>
        <?php
    }

    /**
     * Render premium limit field
     */
    public function render_premium_limit_field() {
        $options = get_option( 'vmi_media_limits', array() );
        $premium_limit = isset( $options['premium_limit'] ) ? $options['premium_limit'] : 20;
        ?>
        <input type="number" name="vmi_media_limits[premium_limit]" value="<?php echo esc_attr( $premium_limit ); ?>" min="0" />
        <p class="description"><?php esc_html_e( 'Maximum number of media items for premium plan users.', 'voxel-media-integrator' ); ?></p>
        <?php
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts() {
        wp_enqueue_style( 'vmi-admin-style', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/css/admin.css', array(), '1.0.0' );
        wp_enqueue_script( 'vmi-admin-script', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/js/admin.js', array( 'jquery', 'wp-util' ), '1.0.0', true );

        // Add Chart.js for reports
        wp_enqueue_script( 'vmi-chart-js', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/js/chart.min.js', array(), '3.7.0', true );

        // Add localization
        wp_localize_script( 'vmi-admin-script', 'vmiAdmin', array(
            'ajaxUrl' => admin_url( 'admin-ajax.php' ),
            'nonce' => wp_create_nonce( 'vmi-admin-nonce' ),
        ) );
    }

    /**
     * Dismiss field notice
     */
    public function dismiss_field_notice() {
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( sanitize_text_field( wp_unslash( $_POST['nonce'] ) ), 'vmi-admin-nonce' ) ) {
            wp_send_json_error( 'Invalid nonce' );
        }

        update_option( 'vmi_field_notice_dismissed', true );
        wp_send_json_success();
    }

    /**
     * Render settings page
     */
    public function render_settings_page() {
        // Get media counts for overview
        $total_tours = wp_count_posts('wpvr_item');
        $published_tours = isset($total_tours->publish) ? $total_tours->publish : 0;

        $total_models = wp_count_posts('vmi_3d_models');
        $published_models = isset($total_models->publish) ? $total_models->publish : 0;

        $total_videos = wp_count_posts('vmi_videos');
        $published_videos = isset($total_videos->publish) ? $total_videos->publish : 0;

        $total_courses = wp_count_posts('vmi_courses');
        $published_courses = isset($total_courses->publish) ? $total_courses->publish : 0;

        // Get storage info
        $tours_storage = $this->get_storage_usage('wpvr_item');
        $models_storage = $this->get_storage_usage('vmi_3d_models');
        $videos_storage = $this->get_storage_usage('vmi_videos');
        $courses_storage = $this->get_storage_usage('vmi_courses');
        $total_storage = $tours_storage + $models_storage + $videos_storage + $courses_storage;
        $storage_limit = 5368709120; // 5GB default limit

        // Include template
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'assets/templates/admin/settings-dashboard.php';
    }

    /**
     * Render 3D models dashboard page
     */
    public function render_3d_models_page() {
        // Get counts
        $total_models = wp_count_posts('vmi_3d_models');
        $published_models = isset($total_models->publish) ? $total_models->publish : 0;
        $draft_models = isset($total_models->draft) ? $total_models->draft : 0;
        $pending_models = isset($total_models->pending) ? $total_models->pending : 0;

        // Get model types
        $glb_models = $this->count_posts_by_meta('vmi_3d_models', 'vmi_model_format', 'glb');
        $gltf_models = $this->count_posts_by_meta('vmi_3d_models', 'vmi_model_format', 'gltf');
        $obj_models = $this->count_posts_by_meta('vmi_3d_models', 'vmi_model_format', 'obj');

        // Get storage info
        $storage_used = $this->get_storage_usage('vmi_3d_models');
        $storage_available = 1073741824; // 1GB default limit

        // Get recent models
        $recent_models = get_posts(array(
            'post_type' => 'vmi_3d_models',
            'posts_per_page' => 5,
            'orderby' => 'date',
            'order' => 'DESC',
        ));

        // Get popular models
        $popular_models = get_posts(array(
            'post_type' => 'vmi_3d_models',
            'posts_per_page' => 5,
            'meta_key' => 'vmi_model_views',
            'orderby' => 'meta_value_num',
            'order' => 'DESC',
        ));

        // Include template
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'assets/templates/admin/models-dashboard.php';
    }

    /**
     * Render video dashboard page
     */
    public function render_video_page() {
        // Get counts
        $total_videos = wp_count_posts('vmi_videos');
        $published_videos = isset($total_videos->publish) ? $total_videos->publish : 0;
        $draft_videos = isset($total_videos->draft) ? $total_videos->draft : 0;
        $pending_videos = isset($total_videos->pending) ? $total_videos->pending : 0;

        // Get video types
        $youtube_videos = $this->count_posts_by_meta('vmi_videos', 'vmi_video_type', 'youtube');
        $vimeo_videos = $this->count_posts_by_meta('vmi_videos', 'vmi_video_type', 'vimeo');
        $self_hosted_videos = $published_videos - $youtube_videos - $vimeo_videos;

        // Get storage info
        $storage_used = $this->get_storage_usage('vmi_videos');
        $storage_available = 1073741824; // 1GB default limit

        // Get recent videos
        $recent_videos = get_posts(array(
            'post_type' => 'vmi_videos',
            'posts_per_page' => 5,
            'orderby' => 'date',
            'order' => 'DESC',
        ));

        // Get popular videos
        $popular_videos = get_posts(array(
            'post_type' => 'vmi_videos',
            'posts_per_page' => 5,
            'meta_key' => 'vmi_video_views',
            'orderby' => 'meta_value_num',
            'order' => 'DESC',
        ));

        // Include template
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'assets/templates/admin/videos-dashboard.php';
    }

    /**
     * Render LMS dashboard page
     */
    public function render_lms_page() {
        // Get counts
        $total_courses = wp_count_posts('vmi_courses');
        $published_courses = isset($total_courses->publish) ? $total_courses->publish : 0;
        $draft_courses = isset($total_courses->draft) ? $total_courses->draft : 0;
        $pending_courses = isset($total_courses->pending) ? $total_courses->pending : 0;

        $total_lessons = wp_count_posts('vmi_lessons');
        $published_lessons = isset($total_lessons->publish) ? $total_lessons->publish : 0;

        $total_quizzes = wp_count_posts('vmi_quizzes');
        $published_quizzes = isset($total_quizzes->publish) ? $total_quizzes->publish : 0;

        // Get recent courses
        $recent_courses = get_posts(array(
            'post_type' => 'vmi_courses',
            'posts_per_page' => 5,
            'orderby' => 'date',
            'order' => 'DESC',
        ));

        // Get popular courses (based on enrollment count)
        $popular_courses = get_posts(array(
            'post_type' => 'vmi_courses',
            'posts_per_page' => 5,
            'meta_key' => 'vmi_enrolled_count',
            'orderby' => 'meta_value_num',
            'order' => 'DESC',
        ));

        // Include dashboard template
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'assets/templates/admin/lms-dashboard.php';
    }

    /**
     * Render video settings page
     */
    public function render_video_settings_page() {
        // Get options
        $options = get_option( 'vmi_video_settings', array() );

        // Include template
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'assets/templates/admin/settings-dashboard.php';
    }

    /**
     * Render field setup page
     */
    public function render_field_setup_settings_page() {
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'Field Setup Instructions', 'voxel-media-integrator' ); ?></h1>

            <div class="card">
                <h2><?php esc_html_e( 'Required Fields', 'voxel-media-integrator' ); ?></h2>
                <p><?php esc_html_e( 'The Voxel Media Integrator requires the following custom fields to be added to the Business post type in Voxel:', 'voxel-media-integrator' ); ?></p>

                <h3><?php esc_html_e( 'Step 1: Navigate to Field Management', 'voxel-media-integrator' ); ?></h3>
                <p><?php esc_html_e( 'Go to WP Admin > Structure > Post Types > Business > Fields.', 'voxel-media-integrator' ); ?></p>

                <h3><?php esc_html_e( 'Step 2: Add Virtual Tours Field', 'voxel-media-integrator' ); ?></h3>
                <p><?php esc_html_e( 'Add a repeater field named "Virtual Tours" with the following subfields:', 'voxel-media-integrator' ); ?></p>
                <ul>
                    <li><?php esc_html_e( 'Type (select: WPVR, 3D Vista)', 'voxel-media-integrator' ); ?></li>
                    <li><?php esc_html_e( 'WPVR Tour (relationship to WPVR post type)', 'voxel-media-integrator' ); ?></li>
                    <li><?php esc_html_e( '3D Vista Embed Code (text)', 'voxel-media-integrator' ); ?></li>
                </ul>

                <h3><?php esc_html_e( 'Step 3: Add 3D Models Field', 'voxel-media-integrator' ); ?></h3>
                <p><?php esc_html_e( 'Add a repeater field named "3D Models" with the following subfields:', 'voxel-media-integrator' ); ?></p>
                <ul>
                    <li><?php esc_html_e( 'Model (relationship to vmi_3d_models post type)', 'voxel-media-integrator' ); ?></li>
                    <li><?php esc_html_e( 'Title (text)', 'voxel-media-integrator' ); ?></li>
                    <li><?php esc_html_e( 'Description (textarea)', 'voxel-media-integrator' ); ?></li>
                </ul>

                <h3><?php esc_html_e( 'Step 4: Add Videos Field', 'voxel-media-integrator' ); ?></h3>
                <p><?php esc_html_e( 'Add a repeater field named "Videos" with the following subfields:', 'voxel-media-integrator' ); ?></p>
                <ul>
                    <li><?php esc_html_e( 'Video (relationship to vmi_videos post type)', 'voxel-media-integrator' ); ?></li>
                    <li><?php esc_html_e( 'Title (text)', 'voxel-media-integrator' ); ?></li>
                    <li><?php esc_html_e( 'Description (textarea)', 'voxel-media-integrator' ); ?></li>
                </ul>

                <h3><?php esc_html_e( 'Step 5: Add Courses Field', 'voxel-media-integrator' ); ?></h3>
                <p><?php esc_html_e( 'Add a repeater field named "Courses" with the following subfields:', 'voxel-media-integrator' ); ?></p>
                <ul>
                    <li><?php esc_html_e( 'Course (relationship to vmi_courses post type)', 'voxel-media-integrator' ); ?></li>
                    <li><?php esc_html_e( 'Featured (yes/no)', 'voxel-media-integrator' ); ?></li>
                </ul>
            </div>

            <div class="card">
                <h2><?php esc_html_e( 'Field IDs', 'voxel-media-integrator' ); ?></h2>
                <p><?php esc_html_e( 'After creating the fields, you need to configure the field IDs in the respective settings pages:', 'voxel-media-integrator' ); ?></p>

                <ul>
                    <li><?php esc_html_e( 'Virtual Tours: Go to Media Integrator > Virtual Tours Settings', 'voxel-media-integrator' ); ?></li>
                    <li><?php esc_html_e( '3D Models: Go to Media Integrator > 3D Models', 'voxel-media-integrator' ); ?></li>
                    <li><?php esc_html_e( 'Videos: Go to Media Integrator > Video Settings', 'voxel-media-integrator' ); ?></li>
                    <li><?php esc_html_e( 'Courses: Go to Media Integrator > LMS Settings', 'voxel-media-integrator' ); ?></li>
                </ul>

                <p><?php esc_html_e( 'Enter the field IDs in the "Business Field ID" setting on each page.', 'voxel-media-integrator' ); ?></p>
            </div>
        </div>
        <?php
    }

    /**
     * Render media dashboard page
     */
    public function render_media_dashboard_page() {
        // Get counts with draft/pending info
        $total_tours = wp_count_posts('wpvr_item');
        $published_tours = isset($total_tours->publish) ? $total_tours->publish : 0;
        $draft_tours = isset($total_tours->draft) ? $total_tours->draft : 0;
        $pending_tours = isset($total_tours->pending) ? $total_tours->pending : 0;

        $total_models = wp_count_posts('vmi_3d_models');
        $published_models = isset($total_models->publish) ? $total_models->publish : 0;
        $draft_models = isset($total_models->draft) ? $total_models->draft : 0;
        $pending_models = isset($total_models->pending) ? $total_models->pending : 0;

        $total_videos = wp_count_posts('vmi_videos');
        $published_videos = isset($total_videos->publish) ? $total_videos->publish : 0;
        $draft_videos = isset($total_videos->draft) ? $total_videos->draft : 0;
        $pending_videos = isset($total_videos->pending) ? $total_videos->pending : 0;

        $total_courses = wp_count_posts('vmi_courses');
        $published_courses = isset($total_courses->publish) ? $total_courses->publish : 0;
        $draft_courses = isset($total_courses->draft) ? $total_courses->draft : 0;
        $pending_courses = isset($total_courses->pending) ? $total_courses->pending : 0;

        $total_lessons = wp_count_posts('vmi_lessons');
        $published_lessons = isset($total_lessons->publish) ? $total_lessons->publish : 0;

        $total_quizzes = wp_count_posts('vmi_quizzes');
        $published_quizzes = isset($total_quizzes->publish) ? $total_quizzes->publish : 0;

        // Calculate totals
        $total_published = $published_tours + $published_models + $published_videos + $published_courses;
        $total_draft = $draft_tours + $draft_models + $draft_videos + $draft_courses;
        $total_pending = $pending_tours + $pending_models + $pending_videos + $pending_courses;

        // Get storage info
        $tours_storage = $this->get_storage_usage('wpvr_item');
        $models_storage = $this->get_storage_usage('vmi_3d_models');
        $videos_storage = $this->get_storage_usage('vmi_videos');
        $courses_storage = $this->get_storage_usage('vmi_courses');

        $total_storage = $tours_storage + $models_storage + $videos_storage + $courses_storage;
        $storage_limit = 5368709120; // 5GB default limit

        // Get recent media from all types
        $recent_media = array_merge(
            get_posts(array(
                'post_type' => 'wpvr_item',
                'posts_per_page' => 5,
                'orderby' => 'date',
                'order' => 'DESC',
            )),
            get_posts(array(
                'post_type' => 'vmi_3d_models',
                'posts_per_page' => 5,
                'orderby' => 'date',
                'order' => 'DESC',
            )),
            get_posts(array(
                'post_type' => 'vmi_videos',
                'posts_per_page' => 5,
                'orderby' => 'date',
                'order' => 'DESC',
            )),
            get_posts(array(
                'post_type' => 'vmi_courses',
                'posts_per_page' => 5,
                'orderby' => 'date',
                'order' => 'DESC',
            ))
        );

        // Sort by date and limit
        usort($recent_media, function($a, $b) {
            return strtotime($b->post_date) - strtotime($a->post_date);
        });
        $recent_media = array_slice($recent_media, 0, 8);

        // Get popular media (based on views/engagement)
        $popular_media = array_merge(
            get_posts(array(
                'post_type' => 'vmi_3d_models',
                'posts_per_page' => 3,
                'meta_key' => 'vmi_model_views',
                'orderby' => 'meta_value_num',
                'order' => 'DESC',
            )),
            get_posts(array(
                'post_type' => 'vmi_videos',
                'posts_per_page' => 3,
                'meta_key' => 'vmi_video_views',
                'orderby' => 'meta_value_num',
                'order' => 'DESC',
            )),
            get_posts(array(
                'post_type' => 'vmi_courses',
                'posts_per_page' => 3,
                'meta_key' => 'vmi_enrolled_count',
                'orderby' => 'meta_value_num',
                'order' => 'DESC',
            ))
        );

        // Include template
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'assets/templates/admin/main-dashboard.php';
    }

    /**
     * Render templates page
     */
    public function render_templates_page() {
        // Define template categories and metadata
        $template_categories = array(
            'video' => array(
                'title' => __('Video Templates', 'voxel-media-integrator'),
                'icon' => 'video-alt3',
                'color' => '#8b5cf6'
            ),
            'tour' => array(
                'title' => __('Virtual Tour Templates', 'voxel-media-integrator'),
                'icon' => 'visibility',
                'color' => '#06b6d4'
            ),
            'model' => array(
                'title' => __('3D Model Templates', 'voxel-media-integrator'),
                'icon' => 'media-interactive',
                'color' => '#10b981'
            ),
            'lms' => array(
                'title' => __('LMS Templates', 'voxel-media-integrator'),
                'icon' => 'welcome-learn-more',
                'color' => '#f59e0b'
            ),
            'gallery' => array(
                'title' => __('Gallery Templates', 'voxel-media-integrator'),
                'icon' => 'format-gallery',
                'color' => '#ef4444'
            )
        );

        // Define template metadata
        $templates_metadata = array(
            'video-grid' => array(
                'title' => __('Video Grid Layout', 'voxel-media-integrator'),
                'description' => __('Responsive grid layout for displaying multiple videos with thumbnails and play buttons.', 'voxel-media-integrator'),
                'category' => 'video',
                'preview_image' => 'video-grid-preview.jpg',
                'features' => array('Responsive', 'Thumbnails', 'Lazy Loading', 'Lightbox'),
                'shortcode' => '[vmi_video_grid columns="3" autoplay="false"]'
            ),
            'video-carousel' => array(
                'title' => __('Video Carousel', 'voxel-media-integrator'),
                'description' => __('Horizontal scrolling carousel for showcasing featured videos.', 'voxel-media-integrator'),
                'category' => 'video',
                'preview_image' => 'video-carousel-preview.jpg',
                'features' => array('Touch Swipe', 'Auto-play', 'Navigation Dots', 'Responsive'),
                'shortcode' => '[vmi_video_carousel slides="3" autoplay="true"]'
            ),
            'video-player' => array(
                'title' => __('Enhanced Video Player', 'voxel-media-integrator'),
                'description' => __('Advanced video player with custom controls and quality selection.', 'voxel-media-integrator'),
                'category' => 'video',
                'preview_image' => 'video-player-preview.jpg',
                'features' => array('Quality Selection', 'Playback Speed', 'Subtitles', 'Fullscreen'),
                'shortcode' => '[vmi_video_player id="123" controls="true"]'
            ),
            'tour-showcase' => array(
                'title' => __('Virtual Tour Showcase', 'voxel-media-integrator'),
                'description' => __('Elegant showcase layout for virtual tours with preview thumbnails.', 'voxel-media-integrator'),
                'category' => 'tour',
                'preview_image' => 'tour-showcase-preview.jpg',
                'features' => array('360° Preview', 'Hotspot Indicators', 'Mobile Optimized', 'Social Sharing'),
                'shortcode' => '[vmi_tour_showcase layout="grid" columns="2"]'
            ),
            'tour-gallery' => array(
                'title' => __('Tour Gallery', 'voxel-media-integrator'),
                'description' => __('Interactive gallery of virtual tours with filtering options.', 'voxel-media-integrator'),
                'category' => 'tour',
                'preview_image' => 'tour-gallery-preview.jpg',
                'features' => array('Category Filter', 'Search', 'Pagination', 'Lightbox'),
                'shortcode' => '[vmi_tour_gallery filter="true" search="true"]'
            ),
            'model-viewer' => array(
                'title' => __('3D Model Viewer', 'voxel-media-integrator'),
                'description' => __('Interactive 3D model viewer with rotation and zoom controls.', 'voxel-media-integrator'),
                'category' => 'model',
                'preview_image' => 'model-viewer-preview.jpg',
                'features' => array('WebGL', 'Touch Controls', 'Fullscreen', 'Loading Animation'),
                'shortcode' => '[vmi_model_viewer id="123" controls="true"]'
            ),
            'model-gallery' => array(
                'title' => __('3D Model Gallery', 'voxel-media-integrator'),
                'description' => __('Gallery layout for showcasing multiple 3D models.', 'voxel-media-integrator'),
                'category' => 'model',
                'preview_image' => 'model-gallery-preview.jpg',
                'features' => array('Grid Layout', 'Hover Preview', 'Quick View', 'Categories'),
                'shortcode' => '[vmi_model_gallery columns="3" hover_preview="true"]'
            ),
            'course-grid' => array(
                'title' => __('Course Grid', 'voxel-media-integrator'),
                'description' => __('Grid layout for displaying LMS courses with progress indicators.', 'voxel-media-integrator'),
                'category' => 'lms',
                'preview_image' => 'course-grid-preview.jpg',
                'features' => array('Progress Bars', 'Enrollment Status', 'Difficulty Levels', 'Ratings'),
                'shortcode' => '[vmi_course_grid columns="3" show_progress="true"]'
            ),
            'course-carousel' => array(
                'title' => __('Course Carousel', 'voxel-media-integrator'),
                'description' => __('Horizontal carousel for featured courses and learning paths.', 'voxel-media-integrator'),
                'category' => 'lms',
                'preview_image' => 'course-carousel-preview.jpg',
                'features' => array('Auto-scroll', 'Navigation', 'Featured Badge', 'Enrollment CTA'),
                'shortcode' => '[vmi_course_carousel featured="true" autoplay="true"]'
            ),
            'media-gallery' => array(
                'title' => __('Mixed Media Gallery', 'voxel-media-integrator'),
                'description' => __('Combined gallery for videos, tours, and 3D models with filtering.', 'voxel-media-integrator'),
                'category' => 'gallery',
                'preview_image' => 'media-gallery-preview.jpg',
                'features' => array('Filter Tabs', 'Masonry Layout', 'Search', 'Pagination'),
                'shortcode' => '[vmi_media_gallery types="video,tour,model" filter="true"]'
            ),
            'media-slider' => array(
                'title' => __('Media Slider', 'voxel-media-integrator'),
                'description' => __('Full-width slider showcasing different types of media content.', 'voxel-media-integrator'),
                'category' => 'gallery',
                'preview_image' => 'media-slider-preview.jpg',
                'features' => array('Full Width', 'Auto-play', 'Transition Effects', 'Navigation'),
                'shortcode' => '[vmi_media_slider autoplay="true" transition="fade"]'
            )
        );

        // Include template
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'assets/templates/admin/templates-dashboard.php';
    }

    // Virtual Tours settings page is now handled by the VMI_Virtual_Tours class

    /**
     * Render students page
     */
    public function render_students_page() {
        // Get all users with student role or who have enrolled in courses
        $students = get_users( array(
            'role__in' => array( 'subscriber', 'customer' ),
            'meta_query' => array(
                'relation' => 'OR',
                array(
                    'key' => 'vmi_enrolled_courses',
                    'compare' => 'EXISTS',
                ),
                array(
                    'key' => 'vmi_completed_courses',
                    'compare' => 'EXISTS',
                ),
            ),
        ) );

        // Calculate student statistics
        $total_students = count($students);
        $active_students = 0;
        $completed_students = 0;

        foreach ($students as $student) {
            $enrolled_courses = get_user_meta( $student->ID, 'vmi_enrolled_courses', true );
            $completed_courses = get_user_meta( $student->ID, 'vmi_completed_courses', true );

            $enrolled_courses = ! empty( $enrolled_courses ) ? $enrolled_courses : array();
            $completed_courses = ! empty( $completed_courses ) ? $completed_courses : array();

            if ( ! empty( $enrolled_courses ) ) {
                $active_students++;
            }

            if ( ! empty( $completed_courses ) ) {
                $completed_students++;
            }
        }

        // Get courses
        $courses = get_posts( array(
            'post_type' => 'vmi_courses',
            'posts_per_page' => -1,
            'post_status' => 'publish',
        ) );
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'Students', 'voxel-media-integrator' ); ?></h1>

            <div class="vmi-dashboard-stats">
                <div class="vmi-stat-box">
                    <div class="vmi-stat-number"><?php echo esc_html( $total_students ); ?></div>
                    <div class="vmi-stat-label"><?php esc_html_e( 'Total Students', 'voxel-media-integrator' ); ?></div>
                </div>

                <div class="vmi-stat-box">
                    <div class="vmi-stat-number"><?php echo esc_html( $active_students ); ?></div>
                    <div class="vmi-stat-label"><?php esc_html_e( 'Active Students', 'voxel-media-integrator' ); ?></div>
                </div>

                <div class="vmi-stat-box">
                    <div class="vmi-stat-number"><?php echo esc_html( $completed_students ); ?></div>
                    <div class="vmi-stat-label"><?php esc_html_e( 'Students with Completed Courses', 'voxel-media-integrator' ); ?></div>
                </div>
            </div>

            <div class="card">
                <h2><?php esc_html_e( 'Student List', 'voxel-media-integrator' ); ?></h2>

                <?php if ( ! empty( $students ) ) : ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php esc_html_e( 'Student', 'voxel-media-integrator' ); ?></th>
                            <th><?php esc_html_e( 'Email', 'voxel-media-integrator' ); ?></th>
                            <th><?php esc_html_e( 'Enrolled Courses', 'voxel-media-integrator' ); ?></th>
                            <th><?php esc_html_e( 'Completed Courses', 'voxel-media-integrator' ); ?></th>
                            <th><?php esc_html_e( 'Progress', 'voxel-media-integrator' ); ?></th>
                            <th><?php esc_html_e( 'Actions', 'voxel-media-integrator' ); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ( $students as $student ) :
                            $enrolled_courses = get_user_meta( $student->ID, 'vmi_enrolled_courses', true );
                            $completed_courses = get_user_meta( $student->ID, 'vmi_completed_courses', true );

                            $enrolled_courses = ! empty( $enrolled_courses ) ? $enrolled_courses : array();
                            $completed_courses = ! empty( $completed_courses ) ? $completed_courses : array();

                            $enrolled_count = count( $enrolled_courses );
                            $completed_count = count( $completed_courses );

                            $progress = $enrolled_count > 0 ? ( $completed_count / $enrolled_count ) * 100 : 0;
                        ?>
                        <tr>
                            <td>
                                <?php echo esc_html( $student->display_name ); ?>
                            </td>
                            <td>
                                <?php echo esc_html( $student->user_email ); ?>
                            </td>
                            <td>
                                <?php echo esc_html( $enrolled_count ); ?>
                            </td>
                            <td>
                                <?php echo esc_html( $completed_count ); ?>
                            </td>
                            <td>
                                <div class="vmi-progress-bar">
                                    <div class="vmi-progress" style="width: <?php echo esc_attr( $progress ); ?>%"></div>
                                </div>
                                <?php echo esc_html( round( $progress ) . '%' ); ?>
                            </td>
                            <td>
                                <a href="<?php echo esc_url( get_edit_user_link( $student->ID ) ); ?>" class="button button-small"><?php esc_html_e( 'View Profile', 'voxel-media-integrator' ); ?></a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php else : ?>
                <p><?php esc_html_e( 'No students found.', 'voxel-media-integrator' ); ?></p>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render reports page
     */
    public function render_reports_page() {
        // Get courses
        $courses = get_posts( array(
            'post_type' => 'vmi_courses',
            'posts_per_page' => -1,
            'post_status' => 'publish',
        ) );

        // Calculate overall statistics
        $total_enrollments = 0;
        $total_completions = 0;
        $overall_avg_score = 0;
        $score_count = 0;

        foreach ( $courses as $course ) {
            $enrolled_count = get_post_meta( $course->ID, 'vmi_enrolled_count', true );
            $enrolled_count = ! empty( $enrolled_count ) ? $enrolled_count : 0;
            $total_enrollments += $enrolled_count;

            $completed_count = get_post_meta( $course->ID, 'vmi_completed_count', true );
            $completed_count = ! empty( $completed_count ) ? $completed_count : 0;
            $total_completions += $completed_count;

            $avg_score = get_post_meta( $course->ID, 'vmi_avg_quiz_score', true );
            if ( ! empty( $avg_score ) ) {
                $overall_avg_score += $avg_score;
                $score_count++;
            }
        }

        $overall_avg_score = $score_count > 0 ? $overall_avg_score / $score_count : 0;
        $overall_completion_rate = $total_enrollments > 0 ? ( $total_completions / $total_enrollments ) * 100 : 0;
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'LMS Reports', 'voxel-media-integrator' ); ?></h1>

            <div class="vmi-dashboard-card">
                <h3><span class="dashicons dashicons-chart-bar"></span> <?php esc_html_e( 'Overall Statistics', 'voxel-media-integrator' ); ?></h3>

                <div class="vmi-dashboard-stats">
                    <div class="vmi-stat-box">
                        <div class="vmi-stat-number"><?php echo esc_html( $total_enrollments ); ?></div>
                        <div class="vmi-stat-label"><?php esc_html_e( 'Total Enrollments', 'voxel-media-integrator' ); ?></div>
                    </div>

                    <div class="vmi-stat-box">
                        <div class="vmi-stat-number"><?php echo esc_html( $total_completions ); ?></div>
                        <div class="vmi-stat-label"><?php esc_html_e( 'Total Completions', 'voxel-media-integrator' ); ?></div>
                    </div>

                    <div class="vmi-stat-box">
                        <div class="vmi-stat-number"><?php echo esc_html( round($overall_avg_score) . '%' ); ?></div>
                        <div class="vmi-stat-label"><?php esc_html_e( 'Avg. Quiz Score', 'voxel-media-integrator' ); ?></div>
                    </div>
                </div>

                <div class="vmi-usage-bar">
                    <p><?php esc_html_e( 'Overall Completion Rate', 'voxel-media-integrator' ); ?></p>
                    <div class="vmi-progress-bar">
                        <div class="vmi-progress" style="width: <?php echo esc_attr( $overall_completion_rate ); ?>%"></div>
                    </div>
                    <p><?php echo esc_html( round( $overall_completion_rate ) . '% ' . __( 'of enrolled courses completed', 'voxel-media-integrator' ) ); ?></p>
                </div>
            </div>

            <!-- Course Performance Card -->
            <div class="vmi-dashboard-card">
                <h3><span class="dashicons dashicons-performance"></span> <?php esc_html_e( 'Course Performance', 'voxel-media-integrator' ); ?></h3>

                <?php if ( ! empty( $courses ) ) :
                    // Sort courses by enrollment count
                    usort($courses, function($a, $b) {
                        $a_count = get_post_meta( $a->ID, 'vmi_enrolled_count', true );
                        $a_count = ! empty( $a_count ) ? $a_count : 0;

                        $b_count = get_post_meta( $b->ID, 'vmi_enrolled_count', true );
                        $b_count = ! empty( $b_count ) ? $b_count : 0;

                        return $b_count - $a_count;
                    });

                    // Get top 5 courses
                    $top_courses = array_slice($courses, 0, 5);
                ?>
                    <div class="vmi-chart-container">
                        <canvas id="coursePerformanceChart" width="400" height="200"></canvas>
                    </div>

                    <script>
                    jQuery(document).ready(function($) {
                        var ctx = document.getElementById('coursePerformanceChart').getContext('2d');
                        var courseChart = new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: [
                                    <?php
                                    foreach ( $top_courses as $course ) {
                                        echo "'" . esc_js( $course->post_title ) . "', ";
                                    }
                                    ?>
                                ],
                                datasets: [{
                                    label: '<?php esc_html_e( 'Enrolled Students', 'voxel-media-integrator' ); ?>',
                                    data: [
                                        <?php
                                        foreach ( $top_courses as $course ) {
                                            $enrolled_count = get_post_meta( $course->ID, 'vmi_enrolled_count', true );
                                            $enrolled_count = ! empty( $enrolled_count ) ? $enrolled_count : 0;
                                            echo esc_js( $enrolled_count ) . ', ';
                                        }
                                        ?>
                                    ],
                                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                                    borderColor: 'rgba(54, 162, 235, 1)',
                                    borderWidth: 1
                                }, {
                                    label: '<?php esc_html_e( 'Completed Students', 'voxel-media-integrator' ); ?>',
                                    data: [
                                        <?php
                                        foreach ( $top_courses as $course ) {
                                            $completed_count = get_post_meta( $course->ID, 'vmi_completed_count', true );
                                            $completed_count = ! empty( $completed_count ) ? $completed_count : 0;
                                            echo esc_js( $completed_count ) . ', ';
                                        }
                                        ?>
                                    ],
                                    backgroundColor: 'rgba(75, 192, 192, 0.5)',
                                    borderColor: 'rgba(75, 192, 192, 1)',
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                scales: {
                                    y: {
                                        beginAtZero: true
                                    }
                                }
                            }
                        });
                    });
                    </script>
                <?php endif; ?>
            </div>

            <div class="card">
                <h2><?php esc_html_e( 'Course Performance', 'voxel-media-integrator' ); ?></h2>

                <?php if ( ! empty( $courses ) ) : ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php esc_html_e( 'Course', 'voxel-media-integrator' ); ?></th>
                            <th><?php esc_html_e( 'Enrolled Students', 'voxel-media-integrator' ); ?></th>
                            <th><?php esc_html_e( 'Completion Rate', 'voxel-media-integrator' ); ?></th>
                            <th><?php esc_html_e( 'Average Quiz Score', 'voxel-media-integrator' ); ?></th>
                            <th><?php esc_html_e( 'Actions', 'voxel-media-integrator' ); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ( $courses as $course ) :
                            $enrolled_count = get_post_meta( $course->ID, 'vmi_enrolled_count', true );
                            $enrolled_count = ! empty( $enrolled_count ) ? $enrolled_count : 0;

                            $completed_count = get_post_meta( $course->ID, 'vmi_completed_count', true );
                            $completed_count = ! empty( $completed_count ) ? $completed_count : 0;

                            $completion_rate = $enrolled_count > 0 ? ( $completed_count / $enrolled_count ) * 100 : 0;

                            $avg_score = get_post_meta( $course->ID, 'vmi_avg_quiz_score', true );
                            $avg_score = ! empty( $avg_score ) ? $avg_score : 0;
                        ?>
                        <tr>
                            <td>
                                <?php echo esc_html( $course->post_title ); ?>
                            </td>
                            <td>
                                <?php echo esc_html( $enrolled_count ); ?>
                            </td>
                            <td>
                                <div class="vmi-progress-bar">
                                    <div class="vmi-progress" style="width: <?php echo esc_attr( $completion_rate ); ?>%"></div>
                                </div>
                                <?php echo esc_html( round( $completion_rate ) . '%' ); ?>
                            </td>
                            <td>
                                <?php echo esc_html( round( $avg_score ) . '%' ); ?>
                            </td>
                            <td>
                                <a href="<?php echo esc_url( get_edit_post_link( $course->ID ) ); ?>" class="button button-small"><?php esc_html_e( 'View Course', 'voxel-media-integrator' ); ?></a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php else : ?>
                <p><?php esc_html_e( 'No courses found.', 'voxel-media-integrator' ); ?></p>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render LMS settings page
     */
    public function render_lms_settings_page() {
        // Get options
        $options = get_option( 'vmi_lms_settings', array() );

        // Include template
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'assets/templates/admin/settings-dashboard.php';
    }

    /**
     * Count posts by meta value
     *
     * @param string $post_type Post type.
     * @param string $meta_key Meta key.
     * @param string $meta_value Meta value.
     * @return int Count of posts.
     */
    private function count_posts_by_meta( $post_type, $meta_key, $meta_value ) {
        $args = array(
            'post_type' => $post_type,
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => $meta_key,
                    'value' => $meta_value,
                    'compare' => '=',
                ),
            ),
            'fields' => 'ids',
        );

        $query = new WP_Query( $args );
        return $query->found_posts;
    }

    /**
     * Calculate storage usage for a post type
     *
     * @param string $post_type Post type.
     * @return int Storage usage in bytes.
     */
    private function get_storage_usage( $post_type ) {
        $storage_used = 0;

        // Get all posts of the specified type
        $posts = get_posts( array(
            'post_type' => $post_type,
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'fields' => 'ids',
        ) );

        // Loop through posts and calculate storage
        foreach ( $posts as $post_id ) {
            // Get attachments for this post
            $attachments = get_attached_media( '', $post_id );

            // Calculate size of attachments
            foreach ( $attachments as $attachment ) {
                $file_path = get_attached_file( $attachment->ID );
                if ( file_exists( $file_path ) ) {
                    $storage_used += filesize( $file_path );
                }
            }

            // Add meta data size (approximate)
            $meta = get_post_meta( $post_id );
            $meta_size = strlen( serialize( $meta ) );
            $storage_used += $meta_size;
        }

        return $storage_used;
    }

    /**
     * Render virtual tours dashboard page
     */
    public function render_virtual_tours_dashboard_page() {
        // Get counts
        $total_tours = wp_count_posts('vmi_virtual_tours');
        $published_tours = $total_tours->publish;
        $draft_tours = $total_tours->draft;
        $pending_tours = $total_tours->pending;

        // Get tour types
        $wpvr_tours = $this->count_posts_by_meta('vmi_virtual_tours', 'vmi_tour_type', 'wpvr');
        $vista_tours = $this->count_posts_by_meta('vmi_virtual_tours', 'vmi_tour_type', 'vista');
        $custom_tours = $published_tours - $wpvr_tours - $vista_tours;

        // Get storage info
        $storage_used = $this->get_storage_usage('vmi_virtual_tours');
        $storage_available = 1073741824; // 1GB default limit

        // Get recent tours
        $recent_tours = get_posts(array(
            'post_type' => 'vmi_virtual_tours',
            'posts_per_page' => 5,
            'orderby' => 'date',
            'order' => 'DESC',
        ));

        // Get popular tours
        $popular_tours = get_posts(array(
            'post_type' => 'vmi_virtual_tours',
            'posts_per_page' => 5,
            'meta_key' => 'vmi_tour_views',
            'orderby' => 'meta_value_num',
            'order' => 'DESC',
        ));

        // Include dashboard template
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'assets/templates/admin/virtual-tours-dashboard.php';
    }

    /**
     * Render virtual tours settings page
     */
    public function render_virtual_tours_settings_page() {
        // Get options
        $options = get_option( 'vmi_virtual_tours_settings', array() );

        // Include settings template
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'assets/templates/admin/settings-dashboard.php';
    }

    /**
     * Render the main plugin settings page
     */
    public function render_plugin_settings_page() {
        // Settings groups for tabs
        $settings_groups = array(
            'vmi_settings' => __('General', 'voxel-media-integrator'),
            'vmi_3d_models_settings' => __('3D Models', 'voxel-media-integrator'),
            'vmi_videos_settings' => __('Videos', 'voxel-media-integrator'),
            'vmi_virtual_tours_settings' => __('Virtual Tours', 'voxel-media-integrator'),
            'vmi_lms_settings' => __('LMS', 'voxel-media-integrator')
        );

        $active_tab = isset($_GET['tab']) && array_key_exists($_GET['tab'], $settings_groups) ? sanitize_key($_GET['tab']) : 'vmi_settings';

        // Provide variables for the template
        $configuration_status = 85; // Mock percentage
        $active_integrations = 3;
        $performance_score = 92;

        // Create some mock activity data
        $recent_activity = array(
            array('title' => 'Settings Updated', 'time' => '2 hours ago'),
            array('title' => 'Integration Enabled', 'time' => '1 day ago'),
            array('title' => 'Performance Optimized', 'time' => '3 days ago')
        );

        // Include the template
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'assets/templates/admin/settings-dashboard.php';
    }
}