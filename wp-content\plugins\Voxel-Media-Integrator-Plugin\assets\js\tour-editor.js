/**
 * Virtual Tour Editor
 * 
 * Provides functionality for creating and editing 360° panorama tours
 */

(function($) {
    'use strict';

    // Tour Editor class
    var TourEditor = function(options) {
        this.options = $.extend({
            container: '#vmi-tour-editor',
            previewContainer: '#vmi-tour-preview',
            saveButton: '#vmi-save-tour',
            addSceneButton: '#vmi-add-scene',
            addHotspotButton: '#vmi-add-hotspot',
            scenesList: '#vmi-scenes-list',
            hotspotsList: '#vmi-hotspots-list',
            uploadButton: '#vmi-upload-panorama',
            configInput: '#vmi_pannellum_config',
            ajaxUrl: '',
            nonce: '',
            tourId: 0
        }, options);

        this.scenes = {};
        this.currentScene = null;
        this.viewer = null;
        this.init();
    };

    // Initialize the editor
    TourEditor.prototype.init = function() {
        var self = this;
        
        // Try to load existing configuration
        var configInput = $(this.options.configInput);
        if (configInput.length && configInput.val()) {
            try {
                var config = JSON.parse(configInput.val());
                this.scenes = config.scenes || {};
                this.currentScene = config.default.firstScene || Object.keys(this.scenes)[0] || null;
            } catch (e) {
                console.error('Error parsing tour configuration:', e);
                this.scenes = {};
                this.currentScene = null;
            }
        }
        
        // Initialize the viewer if we have scenes
        if (Object.keys(this.scenes).length > 0) {
            this.initViewer();
        }
        
        // Initialize UI
        this.initUI();
        
        // Bind events
        this.bindEvents();
    };
    
    // Initialize the panorama viewer
    TourEditor.prototype.initViewer = function() {
        var self = this;
        var previewContainer = $(this.options.previewContainer);
        
        if (previewContainer.length === 0) {
            return;
        }
        
        // Clear the container
        previewContainer.empty();
        
        // Create configuration for the viewer
        var viewerConfig = {
            default: {
                firstScene: this.currentScene || Object.keys(this.scenes)[0],
                sceneFadeDuration: 1000
            },
            scenes: this.scenes
        };
        
        // Initialize the viewer
        if (typeof pannellum !== 'undefined') {
            this.viewer = pannellum.viewer(
                previewContainer.attr('id'),
                viewerConfig
            );
            
            // Add event listener for hotspot clicks
            this.viewer.on('click', function(e) {
                // Handle hotspot clicks
            });
        }
    };
    
    // Initialize the UI
    TourEditor.prototype.initUI = function() {
        var self = this;
        
        // Populate scenes list
        this.updateScenesList();
        
        // Populate hotspots list if we have a current scene
        if (this.currentScene && this.scenes[this.currentScene]) {
            this.updateHotspotsList();
        }
    };
    
    // Update the scenes list in the UI
    TourEditor.prototype.updateScenesList = function() {
        var self = this;
        var scenesList = $(this.options.scenesList);
        
        if (scenesList.length === 0) {
            return;
        }
        
        // Clear the list
        scenesList.empty();
        
        // Add each scene to the list
        $.each(this.scenes, function(id, scene) {
            var sceneItem = $('<li class="vmi-scene-item"></li>');
            var sceneTitle = $('<span class="vmi-scene-title"></span>').text(scene.title || id);
            var sceneActions = $('<div class="vmi-scene-actions"></div>');
            
            var editButton = $('<button type="button" class="button vmi-edit-scene"></button>').text('Edit');
            var deleteButton = $('<button type="button" class="button vmi-delete-scene"></button>').text('Delete');
            
            sceneActions.append(editButton, deleteButton);
            sceneItem.append(sceneTitle, sceneActions);
            sceneItem.attr('data-scene-id', id);
            
            if (self.currentScene === id) {
                sceneItem.addClass('active');
            }
            
            scenesList.append(sceneItem);
        });
    };
    
    // Update the hotspots list for the current scene
    TourEditor.prototype.updateHotspotsList = function() {
        var self = this;
        var hotspotsList = $(this.options.hotspotsList);
        
        if (hotspotsList.length === 0 || !this.currentScene || !this.scenes[this.currentScene]) {
            return;
        }
        
        // Clear the list
        hotspotsList.empty();
        
        // Get hotspots for the current scene
        var hotspots = this.scenes[this.currentScene].hotSpots || [];
        
        // Add each hotspot to the list
        $.each(hotspots, function(index, hotspot) {
            var hotspotItem = $('<li class="vmi-hotspot-item"></li>');
            var hotspotTitle = $('<span class="vmi-hotspot-title"></span>').text(hotspot.text || 'Hotspot ' + (index + 1));
            var hotspotActions = $('<div class="vmi-hotspot-actions"></div>');
            
            var editButton = $('<button type="button" class="button vmi-edit-hotspot"></button>').text('Edit');
            var deleteButton = $('<button type="button" class="button vmi-delete-hotspot"></button>').text('Delete');
            
            hotspotActions.append(editButton, deleteButton);
            hotspotItem.append(hotspotTitle, hotspotActions);
            hotspotItem.attr('data-hotspot-index', index);
            
            hotspotsList.append(hotspotItem);
        });
    };
    
    // Bind events
    TourEditor.prototype.bindEvents = function() {
        var self = this;
        
        // Add scene button
        $(this.options.addSceneButton).on('click', function(e) {
            e.preventDefault();
            self.showAddSceneDialog();
        });
        
        // Add hotspot button
        $(this.options.addHotspotButton).on('click', function(e) {
            e.preventDefault();
            self.showAddHotspotDialog();
        });
        
        // Upload panorama button
        $(this.options.uploadButton).on('click', function(e) {
            e.preventDefault();
            self.uploadPanorama();
        });
        
        // Save button
        $(this.options.saveButton).on('click', function(e) {
            e.preventDefault();
            self.saveTour();
        });
        
        // Scene item click
        $(document).on('click', '.vmi-scene-item', function(e) {
            if (!$(e.target).hasClass('button')) {
                var sceneId = $(this).data('scene-id');
                self.switchScene(sceneId);
            }
        });
        
        // Edit scene button
        $(document).on('click', '.vmi-edit-scene', function(e) {
            e.preventDefault();
            var sceneId = $(this).closest('.vmi-scene-item').data('scene-id');
            self.showEditSceneDialog(sceneId);
        });
        
        // Delete scene button
        $(document).on('click', '.vmi-delete-scene', function(e) {
            e.preventDefault();
            var sceneId = $(this).closest('.vmi-scene-item').data('scene-id');
            self.deleteScene(sceneId);
        });
        
        // Edit hotspot button
        $(document).on('click', '.vmi-edit-hotspot', function(e) {
            e.preventDefault();
            var hotspotIndex = $(this).closest('.vmi-hotspot-item').data('hotspot-index');
            self.showEditHotspotDialog(hotspotIndex);
        });
        
        // Delete hotspot button
        $(document).on('click', '.vmi-delete-hotspot', function(e) {
            e.preventDefault();
            var hotspotIndex = $(this).closest('.vmi-hotspot-item').data('hotspot-index');
            self.deleteHotspot(hotspotIndex);
        });
    };
    
    // Show dialog to add a new scene
    TourEditor.prototype.showAddSceneDialog = function() {
        var self = this;
        
        // Create dialog HTML
        var dialog = $('<div id="vmi-add-scene-dialog" title="Add New Scene"></div>');
        var form = $('<form></form>');
        
        form.append('<div class="form-field"><label for="vmi-scene-id">Scene ID:</label><input type="text" id="vmi-scene-id" name="scene_id" required></div>');
        form.append('<div class="form-field"><label for="vmi-scene-title">Title:</label><input type="text" id="vmi-scene-title" name="title" required></div>');
        form.append('<div class="form-field"><label for="vmi-scene-panorama">Panorama URL:</label><input type="text" id="vmi-scene-panorama" name="panorama" required><button type="button" id="vmi-select-panorama" class="button">Select Image</button></div>');
        
        dialog.append(form);
        
        // Add dialog to the page
        $('body').append(dialog);
        
        // Initialize dialog
        dialog.dialog({
            modal: true,
            width: 500,
            buttons: {
                "Add Scene": function() {
                    var sceneId = $('#vmi-scene-id').val();
                    var title = $('#vmi-scene-title').val();
                    var panorama = $('#vmi-scene-panorama').val();
                    
                    if (sceneId && title && panorama) {
                        self.addScene(sceneId, {
                            title: title,
                            panorama: panorama,
                            hotSpots: []
                        });
                        
                        $(this).dialog("close");
                    }
                },
                Cancel: function() {
                    $(this).dialog("close");
                }
            },
            close: function() {
                $(this).dialog("destroy").remove();
            }
        });
        
        // Initialize media uploader for panorama selection
        $('#vmi-select-panorama').on('click', function(e) {
            e.preventDefault();
            
            var mediaUploader = wp.media({
                title: 'Select Panorama Image',
                button: {
                    text: 'Use this image'
                },
                multiple: false
            });
            
            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#vmi-scene-panorama').val(attachment.url);
            });
            
            mediaUploader.open();
        });
    };
    
    // Add a new scene
    TourEditor.prototype.addScene = function(sceneId, sceneData) {
        // Add the scene to the scenes object
        this.scenes[sceneId] = sceneData;
        
        // If this is the first scene, set it as the current scene
        if (!this.currentScene) {
            this.currentScene = sceneId;
        }
        
        // Update the UI
        this.updateScenesList();
        
        // Reinitialize the viewer
        this.initViewer();
        
        // Update the configuration input
        this.updateConfigInput();
    };
    
    // Switch to a different scene
    TourEditor.prototype.switchScene = function(sceneId) {
        if (this.scenes[sceneId]) {
            this.currentScene = sceneId;
            
            // Update the UI
            $('.vmi-scene-item').removeClass('active');
            $('.vmi-scene-item[data-scene-id="' + sceneId + '"]').addClass('active');
            
            // Update hotspots list
            this.updateHotspotsList();
            
            // Switch scene in the viewer
            if (this.viewer) {
                this.viewer.loadScene(sceneId);
            }
        }
    };
    
    // Show dialog to edit a scene
    TourEditor.prototype.showEditSceneDialog = function(sceneId) {
        var self = this;
        var scene = this.scenes[sceneId];
        
        if (!scene) {
            return;
        }
        
        // Create dialog HTML
        var dialog = $('<div id="vmi-edit-scene-dialog" title="Edit Scene"></div>');
        var form = $('<form></form>');
        
        form.append('<div class="form-field"><label for="vmi-scene-id">Scene ID:</label><input type="text" id="vmi-scene-id" name="scene_id" value="' + sceneId + '" readonly></div>');
        form.append('<div class="form-field"><label for="vmi-scene-title">Title:</label><input type="text" id="vmi-scene-title" name="title" value="' + (scene.title || '') + '" required></div>');
        form.append('<div class="form-field"><label for="vmi-scene-panorama">Panorama URL:</label><input type="text" id="vmi-scene-panorama" name="panorama" value="' + (scene.panorama || '') + '" required><button type="button" id="vmi-select-panorama" class="button">Select Image</button></div>');
        
        dialog.append(form);
        
        // Add dialog to the page
        $('body').append(dialog);
        
        // Initialize dialog
        dialog.dialog({
            modal: true,
            width: 500,
            buttons: {
                "Save Changes": function() {
                    var title = $('#vmi-scene-title').val();
                    var panorama = $('#vmi-scene-panorama').val();
                    
                    if (title && panorama) {
                        self.updateScene(sceneId, {
                            title: title,
                            panorama: panorama,
                            hotSpots: scene.hotSpots || []
                        });
                        
                        $(this).dialog("close");
                    }
                },
                Cancel: function() {
                    $(this).dialog("close");
                }
            },
            close: function() {
                $(this).dialog("destroy").remove();
            }
        });
        
        // Initialize media uploader for panorama selection
        $('#vmi-select-panorama').on('click', function(e) {
            e.preventDefault();
            
            var mediaUploader = wp.media({
                title: 'Select Panorama Image',
                button: {
                    text: 'Use this image'
                },
                multiple: false
            });
            
            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#vmi-scene-panorama').val(attachment.url);
            });
            
            mediaUploader.open();
        });
    };
    
    // Update a scene
    TourEditor.prototype.updateScene = function(sceneId, sceneData) {
        // Update the scene in the scenes object
        this.scenes[sceneId] = sceneData;
        
        // Update the UI
        this.updateScenesList();
        
        // Reinitialize the viewer
        this.initViewer();
        
        // Update the configuration input
        this.updateConfigInput();
    };
    
    // Delete a scene
    TourEditor.prototype.deleteScene = function(sceneId) {
        var self = this;
        
        // Confirm deletion
        if (confirm('Are you sure you want to delete this scene?')) {
            // Remove the scene from the scenes object
            delete this.scenes[sceneId];
            
            // If this was the current scene, switch to another scene
            if (this.currentScene === sceneId) {
                var sceneIds = Object.keys(this.scenes);
                this.currentScene = sceneIds.length > 0 ? sceneIds[0] : null;
            }
            
            // Update the UI
            this.updateScenesList();
            
            // Update hotspots list
            this.updateHotspotsList();
            
            // Reinitialize the viewer
            this.initViewer();
            
            // Update the configuration input
            this.updateConfigInput();
        }
    };
    
    // Show dialog to add a new hotspot
    TourEditor.prototype.showAddHotspotDialog = function() {
        var self = this;
        
        if (!this.currentScene || !this.scenes[this.currentScene]) {
            alert('Please select a scene first.');
            return;
        }
        
        // Create dialog HTML
        var dialog = $('<div id="vmi-add-hotspot-dialog" title="Add New Hotspot"></div>');
        var form = $('<form></form>');
        
        form.append('<div class="form-field"><label for="vmi-hotspot-text">Text:</label><input type="text" id="vmi-hotspot-text" name="text" required></div>');
        form.append('<div class="form-field"><label for="vmi-hotspot-type">Type:</label><select id="vmi-hotspot-type" name="type"><option value="info">Info</option><option value="scene">Scene Link</option></select></div>');
        
        // Scene selection (for scene links)
        var sceneSelect = $('<div class="form-field scene-link-field" style="display:none;"><label for="vmi-hotspot-scene">Target Scene:</label><select id="vmi-hotspot-scene" name="sceneId"></select></div>');
        
        // Add scene options
        $.each(this.scenes, function(id, scene) {
            if (id !== self.currentScene) {
                sceneSelect.find('select').append('<option value="' + id + '">' + (scene.title || id) + '</option>');
            }
        });
        
        form.append(sceneSelect);
        
        // URL field (for info hotspots)
        form.append('<div class="form-field info-field"><label for="vmi-hotspot-url">URL (optional):</label><input type="text" id="vmi-hotspot-url" name="URL"></div>');
        
        // Position fields
        form.append('<div class="form-field"><label for="vmi-hotspot-pitch">Pitch:</label><input type="number" id="vmi-hotspot-pitch" name="pitch" value="0" step="0.1"></div>');
        form.append('<div class="form-field"><label for="vmi-hotspot-yaw">Yaw:</label><input type="number" id="vmi-hotspot-yaw" name="yaw" value="0" step="0.1"></div>');
        
        dialog.append(form);
        
        // Add dialog to the page
        $('body').append(dialog);
        
        // Initialize dialog
        dialog.dialog({
            modal: true,
            width: 500,
            buttons: {
                "Add Hotspot": function() {
                    var text = $('#vmi-hotspot-text').val();
                    var type = $('#vmi-hotspot-type').val();
                    var pitch = parseFloat($('#vmi-hotspot-pitch').val());
                    var yaw = parseFloat($('#vmi-hotspot-yaw').val());
                    
                    var hotspotData = {
                        text: text,
                        type: type,
                        pitch: pitch,
                        yaw: yaw
                    };
                    
                    if (type === 'scene') {
                        hotspotData.sceneId = $('#vmi-hotspot-scene').val();
                    } else {
                        var url = $('#vmi-hotspot-url').val();
                        if (url) {
                            hotspotData.URL = url;
                        }
                    }
                    
                    self.addHotspot(hotspotData);
                    $(this).dialog("close");
                },
                Cancel: function() {
                    $(this).dialog("close");
                }
            },
            close: function() {
                $(this).dialog("destroy").remove();
            }
        });
        
        // Toggle fields based on hotspot type
        $('#vmi-hotspot-type').on('change', function() {
            var type = $(this).val();
            
            if (type === 'scene') {
                $('.scene-link-field').show();
                $('.info-field').hide();
            } else {
                $('.scene-link-field').hide();
                $('.info-field').show();
            }
        });
    };
    
    // Add a new hotspot to the current scene
    TourEditor.prototype.addHotspot = function(hotspotData) {
        if (!this.currentScene || !this.scenes[this.currentScene]) {
            return;
        }
        
        // Initialize hotSpots array if it doesn't exist
        if (!this.scenes[this.currentScene].hotSpots) {
            this.scenes[this.currentScene].hotSpots = [];
        }
        
        // Add the hotspot
        this.scenes[this.currentScene].hotSpots.push(hotspotData);
        
        // Update the UI
        this.updateHotspotsList();
        
        // Reinitialize the viewer
        this.initViewer();
        
        // Update the configuration input
        this.updateConfigInput();
    };
    
    // Show dialog to edit a hotspot
    TourEditor.prototype.showEditHotspotDialog = function(hotspotIndex) {
        var self = this;
        
        if (!this.currentScene || !this.scenes[this.currentScene] || !this.scenes[this.currentScene].hotSpots) {
            return;
        }
        
        var hotspot = this.scenes[this.currentScene].hotSpots[hotspotIndex];
        
        if (!hotspot) {
            return;
        }
        
        // Create dialog HTML
        var dialog = $('<div id="vmi-edit-hotspot-dialog" title="Edit Hotspot"></div>');
        var form = $('<form></form>');
        
        form.append('<div class="form-field"><label for="vmi-hotspot-text">Text:</label><input type="text" id="vmi-hotspot-text" name="text" value="' + (hotspot.text || '') + '" required></div>');
        form.append('<div class="form-field"><label for="vmi-hotspot-type">Type:</label><select id="vmi-hotspot-type" name="type"><option value="info" ' + (hotspot.type === 'info' ? 'selected' : '') + '>Info</option><option value="scene" ' + (hotspot.type === 'scene' ? 'selected' : '') + '>Scene Link</option></select></div>');
        
        // Scene selection (for scene links)
        var sceneSelect = $('<div class="form-field scene-link-field" ' + (hotspot.type !== 'scene' ? 'style="display:none;"' : '') + '><label for="vmi-hotspot-scene">Target Scene:</label><select id="vmi-hotspot-scene" name="sceneId"></select></div>');
        
        // Add scene options
        $.each(this.scenes, function(id, scene) {
            if (id !== self.currentScene) {
                sceneSelect.find('select').append('<option value="' + id + '" ' + (hotspot.sceneId === id ? 'selected' : '') + '>' + (scene.title || id) + '</option>');
            }
        });
        
        form.append(sceneSelect);
        
        // URL field (for info hotspots)
        form.append('<div class="form-field info-field" ' + (hotspot.type !== 'info' ? 'style="display:none;"' : '') + '><label for="vmi-hotspot-url">URL (optional):</label><input type="text" id="vmi-hotspot-url" name="URL" value="' + (hotspot.URL || '') + '"></div>');
        
        // Position fields
        form.append('<div class="form-field"><label for="vmi-hotspot-pitch">Pitch:</label><input type="number" id="vmi-hotspot-pitch" name="pitch" value="' + (hotspot.pitch || 0) + '" step="0.1"></div>');
        form.append('<div class="form-field"><label for="vmi-hotspot-yaw">Yaw:</label><input type="number" id="vmi-hotspot-yaw" name="yaw" value="' + (hotspot.yaw || 0) + '" step="0.1"></div>');
        
        dialog.append(form);
        
        // Add dialog to the page
        $('body').append(dialog);
        
        // Initialize dialog
        dialog.dialog({
            modal: true,
            width: 500,
            buttons: {
                "Save Changes": function() {
                    var text = $('#vmi-hotspot-text').val();
                    var type = $('#vmi-hotspot-type').val();
                    var pitch = parseFloat($('#vmi-hotspot-pitch').val());
                    var yaw = parseFloat($('#vmi-hotspot-yaw').val());
                    
                    var hotspotData = {
                        text: text,
                        type: type,
                        pitch: pitch,
                        yaw: yaw
                    };
                    
                    if (type === 'scene') {
                        hotspotData.sceneId = $('#vmi-hotspot-scene').val();
                    } else {
                        var url = $('#vmi-hotspot-url').val();
                        if (url) {
                            hotspotData.URL = url;
                        }
                    }
                    
                    self.updateHotspot(hotspotIndex, hotspotData);
                    $(this).dialog("close");
                },
                Cancel: function() {
                    $(this).dialog("close");
                }
            },
            close: function() {
                $(this).dialog("destroy").remove();
            }
        });
        
        // Toggle fields based on hotspot type
        $('#vmi-hotspot-type').on('change', function() {
            var type = $(this).val();
            
            if (type === 'scene') {
                $('.scene-link-field').show();
                $('.info-field').hide();
            } else {
                $('.scene-link-field').hide();
                $('.info-field').show();
            }
        });
    };
    
    // Update a hotspot
    TourEditor.prototype.updateHotspot = function(hotspotIndex, hotspotData) {
        if (!this.currentScene || !this.scenes[this.currentScene] || !this.scenes[this.currentScene].hotSpots) {
            return;
        }
        
        // Update the hotspot
        this.scenes[this.currentScene].hotSpots[hotspotIndex] = hotspotData;
        
        // Update the UI
        this.updateHotspotsList();
        
        // Reinitialize the viewer
        this.initViewer();
        
        // Update the configuration input
        this.updateConfigInput();
    };
    
    // Delete a hotspot
    TourEditor.prototype.deleteHotspot = function(hotspotIndex) {
        if (!this.currentScene || !this.scenes[this.currentScene] || !this.scenes[this.currentScene].hotSpots) {
            return;
        }
        
        // Confirm deletion
        if (confirm('Are you sure you want to delete this hotspot?')) {
            // Remove the hotspot
            this.scenes[this.currentScene].hotSpots.splice(hotspotIndex, 1);
            
            // Update the UI
            this.updateHotspotsList();
            
            // Reinitialize the viewer
            this.initViewer();
            
            // Update the configuration input
            this.updateConfigInput();
        }
    };
    
    // Create warning message element
    TourEditor.prototype.showWarning = function(message) {
        const warningEl = document.createElement('div');
        warningEl.className = 'vmi-warning-message';
        warningEl.textContent = message;
        
        // Remove any existing warnings
        document.querySelectorAll('.vmi-warning-message').forEach(el => el.remove());
        
        // Insert the warning at the top of the editor
        const editorEl = document.getElementById('vmi-tour-editor');
        editorEl.insertBefore(warningEl, editorEl.firstChild);
    };

    // Show success message
    TourEditor.prototype.showSuccess = function(message) {
        const successEl = document.createElement('div');
        successEl.className = 'vmi-info-message';
        successEl.textContent = message;
        
        // Remove any existing messages
        document.querySelectorAll('.vmi-info-message').forEach(el => el.remove());
        
        // Insert the message at the top of the editor
        const editorEl = document.getElementById('vmi-tour-editor');
        editorEl.insertBefore(successEl, editorEl.firstChild);
        
        // Remove after 3 seconds
        setTimeout(() => {
            successEl.remove();
        }, 3000);
    };

    // Save the tour configuration
    TourEditor.prototype.saveTour = function() {
        const self = this;
        
        // Update the configuration input
        this.updateConfigInput();
        
        if (this.options.tourId) {
            // Save via AJAX
            $.ajax({
                url: this.options.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'vmi_save_tour_config',
                    nonce: this.options.nonce,
                    tour_id: this.options.tourId,
                    config: JSON.stringify({
                        default: {
                            firstScene: this.currentScene,
                            sceneFadeDuration: 1000
                        },
                        scenes: this.scenes
                    })
                },
                success: function(response) {
                    if (response.success) {
                        self.showSuccess(response.data.message || 'Tour saved successfully!');
                    } else {
                        self.showWarning(response.data.message || 'Error saving tour configuration.');
                    }
                },
                error: function(xhr, status, error) {
                    self.showWarning('Error saving tour: ' + error);
                }
            });
        } else {
            // Update form input only
            this.updateConfigInput();
            this.showWarning('Remember to save the form to keep your changes.');
        }
    };
    
    // Update the configuration input field
    TourEditor.prototype.updateConfigInput = function() {
        var configInput = $(this.options.configInput);
        
        if (configInput.length) {
            var config = {
                default: {
                    firstScene: this.currentScene,
                    sceneFadeDuration: 1000
                },
                scenes: this.scenes
            };
            
            configInput.val(JSON.stringify(config, null, 2));
        }
    };
    
    // Initialize the tour editor
    $(document).ready(function() {
        // Check if we're on a tour editor page
        if ($('#vmi-tour-editor').length) {
            // Initialize the editor
            window.vmiTourEditor = new TourEditor({
                ajaxUrl: vmiTourData.ajaxUrl,
                nonce: vmiTourData.nonce,
                tourId: vmiTourData.tourId
            });
        }
    });

})(jQuery);