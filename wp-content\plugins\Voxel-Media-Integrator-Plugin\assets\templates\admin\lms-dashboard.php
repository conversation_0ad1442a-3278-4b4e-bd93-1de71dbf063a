<?php
/**
 * Template for displaying LMS dashboard
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-lms">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <?php if ($draft_courses > 0): ?>
        <div class="vmi-warning-message">
            <?php echo esc_html(sprintf(
                _n(
                    'You have %d draft course that needs attention.',
                    'You have %d draft courses that need attention.',
                    $draft_courses,
                    'voxel-media-integrator'
                ),
                $draft_courses
            )); ?>
        </div>
    <?php endif; ?>

    <div class="vmi-stats-grid">
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-welcome-learn-more"></span>
                <?php esc_html_e('Total Courses', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($courses_count); ?></p>
            <p class="vmi-stat-label"><?php 
                echo esc_html(sprintf(
                    __('%d published', 'voxel-media-integrator'),
                    $published_courses ?? 0
                )); 
            ?></p>
        </div>
        
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-book"></span>
                <?php esc_html_e('Total Lessons', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($lessons_count); ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('Across all courses', 'voxel-media-integrator'); ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-groups"></span>
                <?php esc_html_e('Students Enrolled', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php 
                $total_students = rand(50, 500); // Mock data - replace with actual
                echo esc_html($total_students);
            ?></p>
            <p class="vmi-stat-label"><?php 
                $completion_rate = rand(60, 90);
                echo esc_html(sprintf(
                    __('%d%% completion rate', 'voxel-media-integrator'),
                    $completion_rate
                )); 
            ?></p>
        </div>
    </div>

    <div class="vmi-quick-actions">
        <a href="<?php echo esc_url(admin_url('post-new.php?post_type=vmi_courses')); ?>" class="button button-primary">
            <span class="dashicons dashicons-plus-alt2"></span>
            <?php esc_html_e('Add New Course', 'voxel-media-integrator'); ?>
        </a>
        
        <a href="<?php echo esc_url(admin_url('post-new.php?post_type=vmi_lessons')); ?>" class="button">
            <span class="dashicons dashicons-plus-alt2"></span>
            <?php esc_html_e('Add New Lesson', 'voxel-media-integrator'); ?>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-lms-settings')); ?>" class="button">
            <span class="dashicons dashicons-admin-generic"></span>
            <?php esc_html_e('LMS Settings', 'voxel-media-integrator'); ?>
        </a>
    </div>

    <div class="vmi-content-grid">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-clock"></span>
                <?php esc_html_e('Recent Courses', 'voxel-media-integrator'); ?>
            </h2>
            
            <?php if (!empty($recent_courses)): ?>
                <?php foreach ($recent_courses as $course): ?>
                    <div class="vmi-activity-item">
                        <div class="vmi-activity-icon">
                            <span class="dashicons dashicons-welcome-learn-more"></span>
                        </div>
                        <div class="vmi-activity-content">
                            <div class="vmi-activity-title">
                                <a href="<?php echo esc_url(get_edit_post_link($course->ID)); ?>">
                                    <?php echo esc_html($course->post_title); ?>
                                </a>
                            </div>
                            <div class="vmi-activity-meta">
                                <?php 
                                    $lessons_in_course = get_posts(array(
                                        'post_type' => 'vmi_lessons',
                                        'meta_query' => array(
                                            array(
                                                'key' => '_vmi_course_id',
                                                'value' => $course->ID,
                                                'compare' => '='
                                            )
                                        ),
                                        'posts_per_page' => -1,
                                        'fields' => 'ids'
                                    ));
                                    
                                    echo sprintf(
                                        '%s &bull; %s',
                                        esc_html(sprintf(
                                            _n('%d lesson', '%d lessons', count($lessons_in_course), 'voxel-media-integrator'),
                                            count($lessons_in_course)
                                        )),
                                        esc_html(sprintf(
                                            __('Added %s ago', 'voxel-media-integrator'),
                                            human_time_diff(get_post_time('U', false, $course), current_time('timestamp'))
                                        ))
                                    );
                                ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="vmi-info-message">
                    <?php esc_html_e('No recent courses found.', 'voxel-media-integrator'); ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-chart-pie"></span>
                <?php esc_html_e('Learning Activity', 'voxel-media-integrator'); ?>
            </h2>
            
            <div class="vmi-type-distribution">
                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-welcome-learn-more"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('New Course Created', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php esc_html_e('2 hours ago', 'voxel-media-integrator'); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-groups"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('New Student Enrolled', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php esc_html_e('1 day ago', 'voxel-media-integrator'); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-awards"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Course Completed', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php esc_html_e('3 days ago', 'voxel-media-integrator'); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>